{"rounds": [{"round": 1, "timestamp": "2025-08-18T13:22:26.334448", "round_results": {"round_number": 1, "participating_devices": 8, "round_time": 2.9702847003936768, "distribution_time": 1.2195653915405273, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.742783546447754, "average_accuracy": 0.0}, "communication_cost": 0.12470166015625}, "global_metrics": {}}, {"round": 2, "timestamp": "2025-08-18T13:22:30.195730", "round_results": {"round_number": 2, "participating_devices": 8, "round_time": 2.8597829341888428, "distribution_time": 1.1543285846710205, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.6982600688934326, "average_accuracy": 0.0}, "communication_cost": 0.0877431640625}, "global_metrics": {}}, {"round": 3, "timestamp": "2025-08-18T13:22:33.693254", "round_results": {"round_number": 3, "participating_devices": 8, "round_time": 2.4936952590942383, "distribution_time": 0.9542124271392822, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.5344181060791016, "average_accuracy": 0.0}, "communication_cost": 0.06491162109375}, "global_metrics": {}}, {"round": 4, "timestamp": "2025-08-18T13:22:38.689647", "round_results": {"round_number": 4, "participating_devices": 8, "round_time": 3.9941446781158447, "distribution_time": 2.3045339584350586, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.6834557056427002, "average_accuracy": 0.0}, "communication_cost": 0.1186044921875}, "global_metrics": {}}, {"round": 5, "timestamp": "2025-08-18T13:22:42.776821", "round_results": {"round_number": 5, "participating_devices": 8, "round_time": 3.0844855308532715, "distribution_time": 2.165545701980591, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 0.9132080078125, "average_accuracy": 0.0}, "communication_cost": 0.02780029296875}, "global_metrics": {}}, {"round": 6, "timestamp": "2025-08-18T13:22:47.783332", "round_results": {"round_number": 6, "participating_devices": 8, "round_time": 4.005153179168701, "distribution_time": 1.8538296222686768, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 2.139893054962158, "average_accuracy": 0.0}, "communication_cost": 0.07332714843749999}, "global_metrics": {}}, {"round": 7, "timestamp": "2025-08-18T13:22:51.607918", "round_results": {"round_number": 7, "participating_devices": 8, "round_time": 2.8220880031585693, "distribution_time": 1.304642677307129, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.511594533920288, "average_accuracy": 0.0}, "communication_cost": 0.11448291015625}, "global_metrics": {}}, {"round": 8, "timestamp": "2025-08-18T13:22:55.537447", "round_results": {"round_number": 8, "participating_devices": 8, "round_time": 2.9243814945220947, "distribution_time": 1.253202199935913, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.6571166515350342, "average_accuracy": 0.0}, "communication_cost": 0.06614843749999999}, "global_metrics": {}}, {"round": 9, "timestamp": "2025-08-18T13:22:59.966641", "round_results": {"round_number": 9, "participating_devices": 8, "round_time": 3.4279072284698486, "distribution_time": 1.1042299270629883, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 2.3171300888061523, "average_accuracy": 0.0}, "communication_cost": 0.15840576171874998}, "global_metrics": {}}, {"round": 10, "timestamp": "2025-08-18T13:23:04.057879", "round_results": {"round_number": 10, "participating_devices": 8, "round_time": 3.0897231101989746, "distribution_time": 0.9528303146362305, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 2.1318447589874268, "average_accuracy": 0.0}, "communication_cost": 0.10687060546874999}, "global_metrics": {}}, {"round": 11, "timestamp": "2025-08-18T13:23:08.275004", "round_results": {"round_number": 11, "participating_devices": 8, "round_time": 3.21593976020813, "distribution_time": 1.853827953338623, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.3569414615631104, "average_accuracy": 0.0}, "communication_cost": 0.08990234375}, "global_metrics": {}}, {"round": 12, "timestamp": "2025-08-18T13:23:12.775924", "round_results": {"round_number": 12, "participating_devices": 8, "round_time": 3.497889995574951, "distribution_time": 1.8558862209320068, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.6365907192230225, "average_accuracy": 0.0}, "communication_cost": 0.14636328125}, "global_metrics": {}}, {"round": 13, "timestamp": "2025-08-18T13:23:17.241405", "round_results": {"round_number": 13, "participating_devices": 8, "round_time": 3.464538097381592, "distribution_time": 1.8708271980285645, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.5878844261169434, "average_accuracy": 0.0}, "communication_cost": 0.14619091796875}, "global_metrics": {}}, {"round": 14, "timestamp": "2025-08-18T13:23:22.240876", "round_results": {"round_number": 14, "participating_devices": 8, "round_time": 3.99835467338562, "distribution_time": 2.335967540740967, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.6576552391052246, "average_accuracy": 0.0}, "communication_cost": 0.09460839843749999}, "global_metrics": {}}, {"round": 15, "timestamp": "2025-08-18T13:23:26.449462", "round_results": {"round_number": 15, "participating_devices": 8, "round_time": 3.207021474838257, "distribution_time": 1.7026150226593018, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.4399237632751465, "average_accuracy": 0.0}, "communication_cost": 0.05288134765625}, "global_metrics": {}}, {"round": 16, "timestamp": "2025-08-18T13:23:31.118687", "round_results": {"round_number": 16, "participating_devices": 8, "round_time": 3.6661393642425537, "distribution_time": 1.3533272743225098, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 2.279308319091797, "average_accuracy": 0.0}, "communication_cost": 0.0266796875}, "global_metrics": {}}, {"round": 17, "timestamp": "2025-08-18T13:23:36.404926", "round_results": {"round_number": 17, "participating_devices": 8, "round_time": 3.6153759956359863, "distribution_time": 1.7034730911254883, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.9053354263305664, "average_accuracy": 0.0}, "communication_cost": 0.05352490234375}, "global_metrics": {}}, {"round": 18, "timestamp": "2025-08-18T13:23:41.621492", "round_results": {"round_number": 18, "participating_devices": 8, "round_time": 3.937978744506836, "distribution_time": 1.7537240982055664, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 2.1778011322021484, "average_accuracy": 0.0}, "communication_cost": 0.035890625}, "global_metrics": {}}, {"round": 19, "timestamp": "2025-08-18T13:23:45.860197", "round_results": {"round_number": 19, "participating_devices": 8, "round_time": 3.2035257816314697, "distribution_time": 1.3581187725067139, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.8362185955047607, "average_accuracy": 0.0}, "communication_cost": 0.13235009765625}, "global_metrics": {}}, {"round": 20, "timestamp": "2025-08-18T13:23:50.511723", "round_results": {"round_number": 20, "participating_devices": 8, "round_time": 3.649481773376465, "distribution_time": 1.8503198623657227, "aggregation_time": 0, "training_results": {"successful_devices": 8, "failed_devices": 0, "total_training_time": 1.79361891746521, "average_accuracy": 0.0}, "communication_cost": 0.17184033203125001}, "global_metrics": {}}], "device_metrics": [{"round": 1, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4845750331878662, "inference_time": 0, "memory_usage": 1.2890625, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6557190418243408, "inference_time": 0, "memory_usage": 0.6015625, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": false, "is_processing": false, "metrics": {"training_time": 0, "inference_time": 0, "memory_usage": 0, "model_size": 0, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 0}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4368722438812256, "inference_time": 0, "memory_usage": 0.71484375, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03449249267578125, "inference_time": 0, "memory_usage": 0.03125, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.011229276657104492, "inference_time": 0, "memory_usage": 0.3359375, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": false, "is_processing": false, "metrics": {"training_time": 0, "inference_time": 0, "memory_usage": 0, "model_size": 0, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 0}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04733848571777344, "inference_time": 0, "memory_usage": 0.07421875, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006834745407104492, "inference_time": 0, "memory_usage": 0.0078125, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.06572222709655762, "inference_time": 0, "memory_usage": 9.02734375, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 2, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3805830478668213, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7833905220031738, "inference_time": 0, "memory_usage": 4.93359375, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.44249939918518066, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4368722438812256, "inference_time": 0, "memory_usage": 0.71484375, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03493475914001465, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006337404251098633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007791996002197266, "inference_time": 0, "memory_usage": 0.00390625, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04733848571777344, "inference_time": 0, "memory_usage": 0.07421875, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.017798662185668945, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.024924278259277344, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 3, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3373444080352783, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6922156810760498, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.44249939918518066, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4229147434234619, "inference_time": 0, "memory_usage": 0.00390625, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03005671501159668, "inference_time": 0, "memory_usage": 0.015625, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006337404251098633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0064716339111328125, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.021826505661010742, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.004168510437011719, "inference_time": 0, "memory_usage": 0.0078125, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.01941990852355957, "inference_time": 0, "memory_usage": 0.11328125, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 4, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3861818313598633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.695828914642334, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.44249939918518066, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5109469890594482, "inference_time": 0, "memory_usage": 0.04296875, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03267383575439453, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007284641265869141, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006403684616088867, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.022635698318481445, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.004168510437011719, "inference_time": 0, "memory_usage": 0.0078125, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.021500110626220703, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 5, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.39334917068481445, "inference_time": 0, "memory_usage": 0.0078125, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.695828914642334, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4058554172515869, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5109469890594482, "inference_time": 0, "memory_usage": 0.04296875, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03485393524169922, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.00662994384765625, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006338357925415039, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.023900747299194336, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0048940181732177734, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.037386417388916016, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 6, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.37856006622314453, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7587671279907227, "inference_time": 0, "memory_usage": 0.0234375, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4741935729980469, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.45825910568237305, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03301525115966797, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.00662994384765625, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005552768707275391, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02597522735595703, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005569934844970703, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.037386417388916016, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 7, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4153883457183838, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7587671279907227, "inference_time": 0, "memory_usage": 0.0234375, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4596092700958252, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5377037525177002, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03505444526672363, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.008187055587768555, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0070002079010009766, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.026305198669433594, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005569934844970703, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02234625816345215, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 8, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4555058479309082, "inference_time": 0, "memory_usage": 0.046875, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7587671279907227, "inference_time": 0, "memory_usage": 0.0234375, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5367259979248047, "inference_time": 0, "memory_usage": 0.046875, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.522951602935791, "inference_time": 0, "memory_usage": 0.125, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.06122851371765137, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.008187055587768555, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005320310592651367, "inference_time": 0, "memory_usage": 0.0390625, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03118729591369629, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.009727239608764648, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0344698429107666, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 9, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4693276882171631, "inference_time": 0, "memory_usage": 0.00390625, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7653489112854004, "inference_time": 0, "memory_usage": 0.0390625, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.46323561668395996, "inference_time": 0, "memory_usage": 0.01953125, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5464920997619629, "inference_time": 0, "memory_usage": 0.09375, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03290891647338867, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007747173309326172, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005320310592651367, "inference_time": 0, "memory_usage": 0.0390625, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02577972412109375, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006289958953857422, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0344698429107666, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 10, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.40503573417663574, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7662732601165771, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4366939067840576, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4331543445587158, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0346682071685791, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007747173309326172, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007166385650634766, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02320694923400879, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006289958953857422, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.025645971298217773, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 11, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.40503573417663574, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7154185771942139, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4514787197113037, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4331543445587158, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.031362056732177734, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007247447967529297, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.009015560150146484, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.06953859329223633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006100893020629883, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.06677961349487305, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 12, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.40503573417663574, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6992752552032471, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4320828914642334, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.43146610260009766, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03300905227661133, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007753610610961914, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.00468134880065918, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.06953859329223633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.0067501068115234375, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.021572351455688477, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 13, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.41424059867858887, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6803774833679199, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.403886079788208, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.43146610260009766, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.03230786323547363, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005255699157714844, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.00468134880065918, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.022691726684570312, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.006020069122314453, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02310490608215332, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 14, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3871314525604248, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7283077239990234, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.403886079788208, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.42840075492858887, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.034955501556396484, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.004756450653076172, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.00468134880065918, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.021195411682128906, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005122184753417969, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04778575897216797, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 15, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3871314525604248, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7311131954193115, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.4581596851348877, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.42840075492858887, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.09765338897705078, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.05853867530822754, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.00543212890625, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.022082090377807617, "inference_time": 0, "memory_usage": 0.0390625, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04463624954223633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.022308349609375, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 16, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.596066951751709, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7311131954193115, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6281912326812744, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6629562377929688, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.177473783493042, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.014781713485717773, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.014081716537475586, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.1304464340209961, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04463624954223633, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.05531024932861328, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 17, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5515124797821045, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7311131954193115, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6281912326812744, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.7216305732727051, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.19661331176757812, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04322981834411621, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.059035301208496094, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.08748769760131836, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.008383989334106445, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.2374422550201416, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 18, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.5175068378448486, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.9263627529144287, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6281912326812744, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6042981147766113, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04752969741821289, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.011768102645874023, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.010518550872802734, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.05211663246154785, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007700443267822266, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.2374422550201416, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 19, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.370941162109375, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6405594348907471, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3708505630493164, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.3875560760498047, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.04752969741821289, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005167484283447266, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.010518550872802734, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02194356918334961, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.01969742774963379, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.019502878189086914, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}, {"round": 20, "devices": [{"device_id": 0, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.33852100372314453, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0058231353759765625, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 1, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.6356534957885742, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.008279800415039062, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 2, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.36055541038513184, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 3, "profile": {"cpu_cores": 1, "memory_mb": 512, "max_model_size_mb": 5, "max_training_samples": 1000, "training_time_limit": 30, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.404498815536499, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0064373016357421875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 1000}, "data_samples": 1000}, {"device_id": 4, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.028593063354492188, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 128}, "data_samples": 128}, {"device_id": 5, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.005055427551269531, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 84}, "data_samples": 84}, {"device_id": 6, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.007925033569335938, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 29}, "data_samples": 29}, {"device_id": 7, "profile": {"cpu_cores": 2, "memory_mb": 1024, "max_model_size_mb": 10, "max_training_samples": 2000, "training_time_limit": 60, "count": 4}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.02194356918334961, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0045948028564453125, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 106}, "data_samples": 106}, {"device_id": 8, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.012816667556762695, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0027599334716796875, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 13}, "data_samples": 13}, {"device_id": 9, "profile": {"cpu_cores": 4, "memory_mb": 2048, "max_model_size_mb": 20, "max_training_samples": 5000, "training_time_limit": 120, "count": 2}, "is_trained": true, "is_processing": false, "metrics": {"training_time": 0.019502878189086914, "inference_time": 0, "memory_usage": 0.0, "model_size": 0.0052089691162109375, "accuracy": 0, "precision": 0, "recall": 0, "f1_score": 0, "samples_processed": 90}, "data_samples": 90}]}], "global_metrics": [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}], "communication_costs": [0.12470166015625, 0.0877431640625, 0.06491162109375, 0.1186044921875, 0.02780029296875, 0.07332714843749999, 0.11448291015625, 0.06614843749999999, 0.15840576171874998, 0.10687060546874999, 0.08990234375, 0.14636328125, 0.14619091796875, 0.09460839843749999, 0.05288134765625, 0.0266796875, 0.05352490234375, 0.035890625, 0.13235009765625, 0.17184033203125001], "resource_usage": [], "summary": {"total_rounds": 20, "final_accuracy": 0, "max_accuracy": 0, "avg_accuracy": 0.0, "final_f1_score": 0, "max_f1_score": 0, "avg_f1_score": 0.0, "total_communication_cost": 1.8932280273437496, "avg_communication_cost": 0.0946614013671875, "num_devices": 10, "simulation_completed": true}}