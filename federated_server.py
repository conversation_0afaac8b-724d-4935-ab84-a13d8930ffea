"""
Federated Learning Server Module
Implements the central server for federated learning coordination and model aggregation.
"""

import numpy as np
import time
import logging
from datetime import datetime
from typing import List, Dict, Any
import pickle
import json
from config import *

class FederatedServer:
    """
    Central server for coordinating federated learning among edge devices.
    Implements FedAvg algorithm with privacy preservation.
    """
    
    def __init__(self):
        self.global_model_weights = None
        self.round_number = 0
        self.participating_devices = []
        
        # Aggregation history
        self.aggregation_history = []
        self.performance_history = []
        
        # Communication simulation
        self.communication_costs = []
        self.round_times = []
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Privacy and security
        self.enable_dp = DIFFERENTIAL_PRIVACY
        self.dp_epsilon = DP_EPSILON
        self.dp_delta = DP_DELTA
        
    def _setup_logging(self):
        """Setup server logging."""
        logger = logging.getLogger('FederatedServer')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(f'{LOGS_DIR}/federated_server.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def select_participating_devices(self, available_devices: List) -> List:
        """
        Select devices to participate in current round.
        Implements client selection strategy for federated learning.
        """
        num_participants = max(
            MIN_CLIENTS_PER_ROUND,
            int(len(available_devices) * CLIENT_PARTICIPATION_RATE)
        )
        
        # Filter out failed devices
        healthy_devices = [d for d in available_devices if not d.simulate_device_failure()]
        
        if len(healthy_devices) < MIN_CLIENTS_PER_ROUND:
            self.logger.warning(f"Only {len(healthy_devices)} healthy devices available")
            return healthy_devices
        
        # Random selection (can be enhanced with more sophisticated strategies)
        selected_indices = np.random.choice(
            len(healthy_devices), 
            min(num_participants, len(healthy_devices)), 
            replace=False
        )
        
        selected_devices = [healthy_devices[i] for i in selected_indices]
        self.participating_devices = [d.device_id for d in selected_devices]
        
        self.logger.info(f"Selected {len(selected_devices)} devices for round {self.round_number + 1}")
        return selected_devices
    
    def coordinate_training_round(self, edge_devices: List) -> Dict[str, Any]:
        """
        Coordinate a complete federated learning round.
        """
        round_start_time = time.time()
        self.round_number += 1
        
        self.logger.info(f"Starting federated learning round {self.round_number}")
        
        # 1. Select participating devices
        participating_devices = self.select_participating_devices(edge_devices)
        
        if len(participating_devices) < MIN_CLIENTS_PER_ROUND:
            self.logger.error(f"Insufficient devices for round {self.round_number}")
            return None
        
        # 2. Distribute global model to selected devices
        distribution_time = self._distribute_global_model(participating_devices)
        
        # 3. Coordinate local training
        training_results = self._coordinate_local_training(participating_devices)
        
        # 4. Collect model updates
        model_updates = self._collect_model_updates(participating_devices)
        
        # 5. Aggregate models
        aggregation_time = self._aggregate_models(model_updates)
        
        # 6. Calculate round metrics
        round_time = time.time() - round_start_time
        self.round_times.append(round_time)
        
        round_results = {
            'round_number': self.round_number,
            'participating_devices': len(participating_devices),
            'round_time': round_time,
            'distribution_time': distribution_time,
            'aggregation_time': aggregation_time,
            'training_results': training_results,
            'communication_cost': self._calculate_communication_cost(model_updates)
        }
        
        self.aggregation_history.append(round_results)
        self.logger.info(f"Round {self.round_number} completed in {round_time:.2f}s")
        
        return round_results
    
    def _distribute_global_model(self, devices: List) -> float:
        """Simulate distributing global model to participating devices."""
        start_time = time.time()
        
        for device in devices:
            # Simulate communication delay
            delay = np.random.choice(COMMUNICATION_DELAY_MS) / 1000.0
            time.sleep(delay)
            
            # Send global model weights
            if self.global_model_weights is not None:
                device.logger.info(f"Received global model for round {self.round_number}")
        
        distribution_time = time.time() - start_time
        self.logger.info(f"Model distribution completed in {distribution_time:.2f}s")
        return distribution_time
    
    def _coordinate_local_training(self, devices: List) -> Dict[str, Any]:
        """Coordinate local training on participating devices."""
        training_results = {
            'successful_devices': 0,
            'failed_devices': 0,
            'total_training_time': 0,
            'average_accuracy': 0
        }
        
        successful_trainings = []
        
        for device in devices:
            try:
                # Train local model
                success = device.train_local_model(self.global_model_weights)
                
                if success:
                    training_results['successful_devices'] += 1
                    training_results['total_training_time'] += device.metrics['training_time']
                    successful_trainings.append(device)
                else:
                    training_results['failed_devices'] += 1
                    
            except Exception as e:
                self.logger.error(f"Training failed for device {device.device_id}: {str(e)}")
                training_results['failed_devices'] += 1
        
        if successful_trainings:
            # Calculate average accuracy (if available)
            accuracies = [d.metrics.get('accuracy', 0) for d in successful_trainings]
            training_results['average_accuracy'] = np.mean(accuracies) if accuracies else 0
        
        return training_results
    
    def _collect_model_updates(self, devices: List) -> List[Dict]:
        """Collect model updates from participating devices."""
        model_updates = []
        
        for device in devices:
            if device.is_trained:
                weights = device.get_model_weights()
                if weights is not None:
                    # Simulate communication cost
                    comm_cost = self._simulate_communication_cost(weights)
                    weights['communication_cost'] = comm_cost
                    model_updates.append(weights)
        
        self.logger.info(f"Collected {len(model_updates)} model updates")
        return model_updates
    
    def _aggregate_models(self, model_updates: List[Dict]) -> float:
        """
        Aggregate model updates using FedAvg algorithm.
        """
        if not model_updates:
            self.logger.warning("No model updates to aggregate")
            return 0
        
        start_time = time.time()
        
        try:
            # Calculate total samples across all devices
            total_samples = sum(update['num_samples'] for update in model_updates)
            
            # Initialize aggregated weights
            aggregated_weights = {}
            
            # Aggregate each parameter
            for param_name in ['coef_', 'intercept_', 'classes_']:
                if param_name in model_updates[0]:
                    weighted_sum = None
                    
                    for update in model_updates:
                        weight = update['num_samples'] / total_samples
                        param_value = update[param_name]
                        
                        if weighted_sum is None:
                            weighted_sum = weight * param_value
                        else:
                            weighted_sum += weight * param_value
                    
                    aggregated_weights[param_name] = weighted_sum
            
            # Apply differential privacy if enabled
            if self.enable_dp:
                aggregated_weights = self._apply_differential_privacy(aggregated_weights)
            
            self.global_model_weights = aggregated_weights
            
            aggregation_time = time.time() - start_time
            self.logger.info(f"Model aggregation completed in {aggregation_time:.2f}s")
            
            return aggregation_time
            
        except Exception as e:
            self.logger.error(f"Model aggregation failed: {str(e)}")
            return 0
    
    def _apply_differential_privacy(self, weights: Dict) -> Dict:
        """Apply differential privacy to aggregated weights."""
        # Simple Gaussian noise addition for differential privacy
        for param_name, param_value in weights.items():
            if isinstance(param_value, np.ndarray):
                noise_scale = 2.0 / (self.dp_epsilon * len(self.participating_devices))
                noise = np.random.normal(0, noise_scale, param_value.shape)
                weights[param_name] = param_value + noise
        
        return weights
    
    def _simulate_communication_cost(self, weights: Dict) -> float:
        """Simulate communication cost based on model size and bandwidth."""
        try:
            # Estimate data size
            data_size_bytes = len(pickle.dumps(weights))
            data_size_kb = data_size_bytes / 1024
            
            # Simulate bandwidth constraint
            bandwidth_kbps = np.random.choice(BANDWIDTH_LIMIT_KBPS)
            transmission_time = data_size_kb / bandwidth_kbps
            
            return transmission_time
            
        except:
            return 0.1  # Default small cost
    
    def _calculate_communication_cost(self, model_updates: List[Dict]) -> float:
        """Calculate total communication cost for the round."""
        total_cost = sum(update.get('communication_cost', 0) for update in model_updates)
        self.communication_costs.append(total_cost)
        return total_cost
    
    def get_global_model_weights(self) -> Dict:
        """Get current global model weights."""
        return self.global_model_weights.copy() if self.global_model_weights else None
    
    def get_aggregation_history(self) -> List[Dict]:
        """Get complete aggregation history."""
        return self.aggregation_history.copy()
    
    def save_global_model(self, filepath: str):
        """Save global model to file."""
        try:
            with open(filepath, 'wb') as f:
                pickle.dump(self.global_model_weights, f)
            self.logger.info(f"Global model saved to {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to save global model: {str(e)}")
    
    def load_global_model(self, filepath: str):
        """Load global model from file."""
        try:
            with open(filepath, 'rb') as f:
                self.global_model_weights = pickle.load(f)
            self.logger.info(f"Global model loaded from {filepath}")
        except Exception as e:
            self.logger.error(f"Failed to load global model: {str(e)}")
    
    def get_server_statistics(self) -> Dict:
        """Get comprehensive server statistics."""
        return {
            'total_rounds': self.round_number,
            'average_round_time': np.mean(self.round_times) if self.round_times else 0,
            'total_communication_cost': sum(self.communication_costs),
            'average_communication_cost': np.mean(self.communication_costs) if self.communication_costs else 0,
            'aggregation_history': self.aggregation_history
        }
