import os
import pandas as pd
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, confusion_matrix
from sklearn.preprocessing import LabelEncoder # Import LabelEncoder here to access its state

# Import functions/classes from our custom modules
from utils.data_loader import load_and_preprocess_data, get_client_data
from utils.metrics import evaluate_model, plot_metrics
from server.aggregator import ModelAggregator

# --- Configuration ---
NUM_CLIENTS = 5 # Number of simulated edge devices
NUM_ROUNDS = 5  # Number of federated learning rounds
DATA_PATH = os.path.join('data', 'raw_dataset.csv')
PROCESSED_DATA_PATH = os.path.join('data', 'processed_dataset.csv')


def train_client_model(X_train, y_train, initial_model=None, global_classes=None): # Added global_classes
    """
    Simulates training a Logistic Regression model on a single edge device.

    Args:
        X_train (pd.DataFrame or np.array): Training features for the client.
        y_train (pd.Series or np.array): Training labels for the client.
        initial_model (LogisticRegression, optional): The global model to
                                                    initialize the client's model.
                                                    If None, starts from scratch.
        global_classes (np.array, optional): An array of all possible target classes
                                             to ensure consistent model output shape.

    Returns:
        tuple: A tuple containing the trained client model and the number of samples trained on.
    """
    if X_train.empty or y_train.empty:
        print("Warning: Client received empty data for training. Skipping training.")
        return LogisticRegression(max_iter=1000, solver='liblinear', random_state=42), 0

    # Initialize client model
    client_model = LogisticRegression(max_iter=1000, solver='liblinear', random_state=42)

    ### FIX FOR CLASS CONSISTENCY: Set classes_ attribute before fit ###
    if global_classes is not None:
        client_model.classes_ = global_classes
        # Ensure the coefficients and intercept are correctly sized if the model isn't fresh
        # This will be handled by the initial_model update below or by fit.

    # If an initial_model (global model) is provided, set the client's model parameters
    # to those of the global model before local training.
    if initial_model:
        if hasattr(initial_model, 'coef_') and hasattr(initial_model, 'intercept_'):
            # Ensure the shapes match. If the number of features changed during preprocessing,
            # this might cause an issue. We assume feature consistency for simplicity.
            if initial_model.coef_.shape[1] == X_train.shape[1] and initial_model.coef_.shape[0] == len(global_classes): # Also check number of classes
                client_model.coef_ = initial_model.coef_
                client_model.intercept_ = initial_model.intercept_
                # print("Client model initialized with global model parameters.")
            else:
                print(f"Warning: Global model feature count ({initial_model.coef_.shape[1]}) or class count ({initial_model.coef_.shape[0]}) does not match client data feature count ({X_train.shape[1]}) or global classes ({len(global_classes)}). Initializing client model from scratch.")
        else:
            # This can happen in the very first round if the global model hasn't been fitted yet
            print("Global model (initial_model) not yet fitted, client model starting from scratch.")
    
    # Train the client's model locally
    try:
        client_model.fit(X_train, y_train)
    except ValueError as e:
        print(f"Error during client training: {e}. Check if data contains NaN or infinite values after preprocessing.")
        return LogisticRegression(max_iter=1000, solver='liblinear', random_state=42), 0

    # After fitting, ensure the client model still has all global_classes,
    # as fit might prune them if a class is completely missing from local data.
    # We re-align coef_ and intercept_ to ensure they match the global_classes structure.
    # This is a bit more involved; for simplicity with LogisticRegression, relying on `classes_`
    # and ensuring the global model has correct shape is usually sufficient.
    # If the `ValueError` persists, this would be the next place to look for more complex alignment.
    
    return client_model, len(X_train)


def main():
    """
    Main function to run the federated learning simulation.
    """
    print("--- Starting Federated Learning IDS Simulation ---")

    # 1. Data Preprocessing
    # Check if processed data already exists
    if os.path.exists(PROCESSED_DATA_PATH):
        print(f"Loading processed data from {PROCESSED_DATA_PATH}")
        processed_df = pd.read_csv(PROCESSED_DATA_PATH)
        label_column = processed_df.columns[-1] # Assuming the last column is the label
        
        # ### FIX FOR CLASS CONSISTENCY: Re-initialize LabelEncoder to get global_classes ###
        # We need the original LabelEncoder to get the global classes it learned.
        # This requires re-fitting it on the entire processed label column.
        temp_le = LabelEncoder()
        temp_le.fit(pd.read_csv(DATA_PATH, low_memory=False)[df.columns[df.columns.str.lower().str.replace(' ', '_').str.replace('/', '_') == label_column][0]])
        global_classes = temp_le.classes_
        # Note: This is a slight re-run of a part of preprocessing.
        # A more elegant solution would be to save/load the LabelEncoder itself.
        # For simplicity, this is a quick fix.
        print("Re-initialized LabelEncoder to retrieve global classes.")

    else:
        print("Processed data not found. Running initial data preprocessing...")
        processed_df, label_column = load_and_preprocess_data(DATA_PATH)
        if processed_df is None:
            print("Exiting: Data preprocessing failed.")
            return
        
        # After first-time preprocessing, get the global classes directly from the LabelEncoder
        # The load_and_preprocess_data function encodes y, so we need access to the le object
        # This is a limitation of the current design.
        # For a truly clean solution, load_and_preprocess_data should return the fitted LabelEncoder.
        # For now, we'll re-fit a temporary one on the *original* label column to get the classes.
        # This is a workaround to avoid significant changes to load_and_preprocess_data's return value.
        temp_df_for_le = pd.read_csv(DATA_PATH, low_memory=False)
        temp_df_for_le.columns = temp_df_for_le.columns.str.strip().str.lower().str.replace(' ', '_').str.replace('/', '_')
        temp_le = LabelEncoder()
        temp_le.fit(temp_df_for_le[label_column].fillna(temp_df_for_le[label_column].mode()[0]))
        global_classes = temp_le.classes_
        print("Retrieved global classes from LabelEncoder after initial preprocessing.")


    # 2. Get client-specific data splits
    client_data_sets = get_client_data(processed_df, NUM_CLIENTS, label_column)
    
    # Extract global test set
    X_test_global, y_test_global = client_data_sets['global_test_set']
    del client_data_sets['global_test_set'] # Remove from client_data_sets for iteration

    # Initialize the model aggregator
    aggregator = ModelAggregator()

    # Lists to store metrics for plotting
    global_accuracies = []
    global_fprs = []

    # 3. Federated Learning Rounds
    for round_num in range(1, NUM_ROUNDS + 1):
        print(f"\n======== Federated Learning Round {round_num} ========")
        
        client_models_for_aggregation = []
        
        # Get the current global model to send to clients
        current_global_model = aggregator.get_global_model()
        
        # Iterate through each client
        for client_id, data_tuple in client_data_sets.items():
            X_train_client, X_test_client, y_train_client, y_test_client = data_tuple
            print(f"  Client {client_id}: Training local model...")
            
            # Train client model, passing the global model for initialization (after round 1)
            # and the global_classes for consistent output shape.
            trained_client_model, num_samples = train_client_model(
                X_train_client, y_train_client, current_global_model, global_classes # <-- CHANGE HERE: Pass global_classes
            )
            
            if num_samples > 0: # Only add models that actually trained on data
                client_models_for_aggregation.append({
                    'model': trained_client_model,
                    'data_size': num_samples
                })
            else:
                print(f"  Client {client_id} did not train due to empty data.")

            # Optional: Evaluate client's local model (can be commented out for less output)
            # evaluate_model(trained_client_model, X_test_client, y_test_client, client_id)

        # 4. Server-side Aggregation
        if round_num == 1 and client_models_for_aggregation:
            # For the very first aggregation, initialize the global model's structure
            # using the first client's model parameters.
            # Crucially, ensure the global model also has the correct classes_ attribute.
            aggregator.initialize_global_model_params(
                client_models_for_aggregation[0]['model'].coef_,
                client_models_for_aggregation[0]['model'].intercept_
            )
            aggregator.global_model.classes_ = global_classes # <-- CRITICAL: Set global model's classes_


        if client_models_for_aggregation:
            aggregated_model = aggregator.aggregate_models(client_models_for_aggregation)
            
            # 5. Evaluate the Aggregated Global Model
            accuracy, fpr, cm = evaluate_model(aggregated_model, X_test_global, y_test_global, "Global Model")
            global_accuracies.append(accuracy)
            global_fprs.append(fpr)
        else:
            print(f"Round {round_num}: No client models available for aggregation. Skipping aggregation and evaluation.")
            global_accuracies.append(0.0)
            global_fprs.append(0.0)


    print("\n--- Simulation Complete ---")
    print("Global Model Accuracies per round:", [f"{acc:.4f}" for acc in global_accuracies])
    print("Global Model FPRs per round:", [f"{fpr:.4f}" for fpr in global_fprs])

    # 6. Plotting Results
    if NUM_ROUNDS > 0 and (global_accuracies or global_fprs):
        plot_metrics(global_accuracies, global_fprs, NUM_ROUNDS)
    else:
        print("Not enough rounds or data to plot metrics.")


if __name__ == "__main__":
    main()

