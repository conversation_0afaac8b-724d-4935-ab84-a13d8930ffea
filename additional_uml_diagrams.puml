@startuml data_flow_diagram
!theme plain
title Edge IDS Data Flow Diagram

package "Data Sources" {
    [Network Traffic] as NT
    [System Logs] as SL
    [Security Events] as SE
}

package "Edge Devices" {
    [Data Collector] as DC
    [Preprocessor] as PP
    [Feature Extractor] as FE
    [Local Storage] as LS
}

package "ML Pipeline" {
    [Model Trainer] as MT
    [Anomaly Detector] as AD
    [Model Updater] as MU
}

package "Federated Learning" {
    [Local Model] as LM
    [Weight Extractor] as WE
    [Global Model Receiver] as GMR
}

package "Communication" {
    [Secure Channel] as SC
    [Privacy Filter] as PF
    [Aggregation Server] as AS
}

package "Output" {
    [Alerts] as AL
    [Reports] as RP
    [Metrics] as ME
}

' Data Flow
NT --> DC : Raw Traffic
SL --> DC : Log Data
SE --> DC : Event Data

DC --> PP : Collected Data
PP --> FE : Cleaned Data
FE --> LS : Features

LS --> MT : Training Data
LS --> AD : Detection Data

MT --> LM : Trained Model
LM --> WE : Model Weights
WE --> PF : Local Updates

PF --> SC : Filtered Updates
SC --> AS : Secure Updates

AS --> GMR : Global Model
GMR --> MU : Updated Weights
MU --> LM : New Model

AD --> AL : Anomalies
AD --> RP : Detection Results
MT --> ME : Training Metrics

note right of PF : "Differential Privacy\nApplied Here"
note left of AS : "FedAvg Aggregation\nPerformed Here"

@enduml

@startuml network_topology
!theme plain
title Edge IDS Network Topology

cloud "Internet" {
    node "Federated Server" as FS {
        component [Global Model Manager]
        component [Aggregation Engine]
        component [Client Coordinator]
    }
}

package "Edge Network 1" {
    node "Edge Gateway 1" as EG1 {
        component [Traffic Monitor]
        component [Local Firewall]
    }
    
    node "Edge Device 1.1" as ED11 {
        component [IDS Agent]
        component [Local Model]
        database "Local Data"
    }
    
    node "Edge Device 1.2" as ED12 {
        component [IDS Agent]
        component [Local Model]
        database "Local Data"
    }
}

package "Edge Network 2" {
    node "Edge Gateway 2" as EG2 {
        component [Traffic Monitor]
        component [Local Firewall]
    }
    
    node "Edge Device 2.1" as ED21 {
        component [IDS Agent]
        component [Local Model]
        database "Local Data"
    }
    
    node "Edge Device 2.2" as ED22 {
        component [IDS Agent]
        component [Local Model]
        database "Local Data"
    }
}

package "Edge Network N" {
    node "Edge Gateway N" as EGN {
        component [Traffic Monitor]
        component [Local Firewall]
    }
    
    node "Edge Device N.1" as EDN1 {
        component [IDS Agent]
        component [Local Model]
        database "Local Data"
    }
}

' Connections
FS <==> EG1 : HTTPS/TLS
FS <==> EG2 : HTTPS/TLS
FS <==> EGN : HTTPS/TLS

EG1 --> ED11 : Local Network
EG1 --> ED12 : Local Network
EG2 --> ED21 : Local Network
EG2 --> ED22 : Local Network
EGN --> EDN1 : Local Network

note right of FS : "Centralized coordination\nwithout data access"
note left of ED11 : "Local processing\nand privacy preservation"

@enduml

@startuml security_architecture
!theme plain
title Edge IDS Security Architecture

package "Security Layers" {
    
    package "Application Security" {
        component [Authentication] as AUTH
        component [Authorization] as AUTHZ
        component [Input Validation] as IV
        component [Secure Coding] as SC
    }
    
    package "Communication Security" {
        component [TLS/SSL Encryption] as TLS
        component [Certificate Management] as CM
        component [Message Integrity] as MI
        component [Replay Protection] as RP
    }
    
    package "Data Security" {
        component [Data Encryption] as DE
        component [Key Management] as KM
        component [Differential Privacy] as DP
        component [Secure Aggregation] as SA
    }
    
    package "Infrastructure Security" {
        component [Network Segmentation] as NS
        component [Firewall Rules] as FR
        component [Intrusion Detection] as ID
        component [Access Control] as AC
    }
    
    package "Privacy Protection" {
        component [Local Data Isolation] as LDI
        component [Gradient Perturbation] as GP
        component [Secure Multi-party Computation] as SMC
        component [Homomorphic Encryption] as HE
    }
}

package "Threat Mitigation" {
    component [Model Poisoning Defense] as MPD
    component [Byzantine Fault Tolerance] as BFT
    component [Adversarial Attack Detection] as AAD
    component [Membership Inference Protection] as MIP
}

' Security Flow
AUTH --> AUTHZ : Verified Identity
AUTHZ --> IV : Authorized Request
IV --> SC : Validated Input

TLS --> CM : Secure Channel
CM --> MI : Verified Certificate
MI --> RP : Integrity Check

DE --> KM : Encrypted Data
KM --> DP : Managed Keys
DP --> SA : Private Data

NS --> FR : Segmented Network
FR --> ID : Filtered Traffic
ID --> AC : Detected Threats

LDI --> GP : Isolated Data
GP --> SMC : Perturbed Gradients
SMC --> HE : Secure Computation

MPD --> BFT : Clean Models
BFT --> AAD : Fault Tolerance
AAD --> MIP : Attack Resistance

@enduml

@startuml performance_monitoring
!theme plain
title Performance Monitoring Architecture

package "Monitoring Components" {
    
    component [Resource Monitor] as RM {
        interface "CPU Usage" as CPU
        interface "Memory Usage" as MEM
        interface "Network I/O" as NET
        interface "Disk I/O" as DISK
    }
    
    component [ML Performance Monitor] as MPM {
        interface "Accuracy Tracker" as ACC
        interface "Training Time" as TT
        interface "Inference Time" as IT
        interface "Model Size" as MS
    }
    
    component [Communication Monitor] as CM {
        interface "Bandwidth Usage" as BW
        interface "Latency Tracker" as LAT
        interface "Message Count" as MC
        interface "Error Rate" as ER
    }
    
    component [Security Monitor] as SM {
        interface "Threat Detection" as TD
        interface "Alert Generation" as AG
        interface "Incident Tracking" as INC
        interface "Compliance Check" as CC
    }
}

package "Data Collection" {
    database "Metrics Database" as MDB
    database "Logs Database" as LDB
    database "Events Database" as EDB
}

package "Analysis & Visualization" {
    component [Real-time Dashboard] as RTD
    component [Historical Analysis] as HA
    component [Predictive Analytics] as PA
    component [Report Generator] as RG
}

package "Alerting System" {
    component [Threshold Monitor] as TM
    component [Anomaly Detector] as AD
    component [Notification Service] as NS
    component [Escalation Manager] as EM
}

' Monitoring Flow
RM --> MDB : Resource Metrics
MPM --> MDB : ML Metrics
CM --> LDB : Communication Logs
SM --> EDB : Security Events

MDB --> RTD : Real-time Data
LDB --> HA : Historical Data
EDB --> PA : Event Data

RTD --> TM : Current Metrics
HA --> AD : Trend Analysis
PA --> NS : Predictions

TM --> NS : Threshold Breach
AD --> EM : Anomaly Alert
NS --> EM : Notifications

@enduml

@startuml evaluation_framework_detail
!theme plain
title Evaluation Framework Detailed Structure

package "Input Data" {
    [Simulation Results] as SR
    [Device Metrics] as DM
    [Global Metrics] as GM
    [Communication Logs] as CL
}

package "Evaluation Modules" {
    
    package "Performance Evaluation" {
        [Accuracy Analysis] as AA
        [Precision/Recall Analysis] as PRA
        [F1-Score Calculation] as F1C
        [Confusion Matrix Generator] as CMG
    }
    
    package "Resource Evaluation" {
        [CPU Efficiency Analysis] as CEA
        [Memory Usage Analysis] as MUA
        [Training Time Analysis] as TTA
        [Model Size Analysis] as MSA
    }
    
    package "Communication Evaluation" {
        [Bandwidth Analysis] as BA
        [Latency Analysis] as LA
        [Message Overhead Analysis] as MOA
        [Protocol Efficiency] as PE
    }
    
    package "Comparison Analysis" {
        [Federated vs Centralized] as FVC
        [Model Comparison] as MC
        [Baseline Comparison] as BC
        [Benchmark Analysis] as BMA
    }
}

package "Output Generation" {
    [Statistical Reports] as STR
    [Visualization Plots] as VP
    [Performance Tables] as PT
    [Recommendation Engine] as RE
}

package "Export Formats" {
    [PDF Reports] as PDF
    [CSV Data] as CSV
    [JSON Results] as JSON
    [LaTeX Tables] as TEX
}

' Evaluation Flow
SR --> AA : Accuracy Data
SR --> PRA : Classification Data
DM --> CEA : Resource Data
DM --> MUA : Memory Data
GM --> F1C : Global Performance
CL --> BA : Communication Data

AA --> STR : Analysis Results
PRA --> VP : Performance Plots
CEA --> PT : Resource Tables
BA --> RE : Efficiency Metrics

STR --> PDF : Formatted Reports
VP --> PDF : Embedded Plots
PT --> CSV : Tabular Data
RE --> JSON : Recommendations

@enduml
