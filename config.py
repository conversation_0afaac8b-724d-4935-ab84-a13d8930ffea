"""
Configuration file for Federated Learning-based Edge IDS
This file contains all the configuration parameters for the simulation.
"""

import os

# ==================== SIMULATION PARAMETERS ====================
# Number of edge devices to simulate
NUM_EDGE_DEVICES = 10

# Federated learning rounds
FL_ROUNDS = 20

# Real-time simulation parameters
REAL_TIME_SIMULATION = True
STREAM_BATCH_SIZE = 100  # Number of samples per batch in real-time stream
STREAM_INTERVAL = 2.0    # Seconds between batches

# ==================== EDGE DEVICE CONSTRAINTS ====================
# Simulate different types of edge devices with varying capabilities
EDGE_DEVICE_PROFILES = {
    'low_resource': {
        'cpu_cores': 1,
        'memory_mb': 512,
        'max_model_size_mb': 5,
        'max_training_samples': 1000,
        'training_time_limit': 30,  # seconds
        'count': 4  # Number of devices with this profile
    },
    'medium_resource': {
        'cpu_cores': 2,
        'memory_mb': 1024,
        'max_model_size_mb': 10,
        'max_training_samples': 2000,
        'training_time_limit': 60,
        'count': 4
    },
    'high_resource': {
        'cpu_cores': 4,
        'memory_mb': 2048,
        'max_model_size_mb': 20,
        'max_training_samples': 5000,
        'training_time_limit': 120,
        'count': 2
    }
}

# ==================== DATASET CONFIGURATION ====================
# Use a subset of data to simulate edge constraints
DATASET_PATH = os.path.join('data', 'raw_dataset.csv')
PROCESSED_DATASET_PATH = os.path.join('data', 'processed_dataset.csv')

# Limit dataset size for edge simulation (use only a subset)
MAX_TOTAL_SAMPLES = 50000  # Much smaller for edge simulation
TRAIN_TEST_SPLIT = 0.8

# Data distribution strategies for federated learning
DATA_DISTRIBUTION = 'non_iid'  # 'iid', 'non_iid', 'unbalanced'

# ==================== MODEL CONFIGURATION ====================
# Lightweight models suitable for edge devices
LIGHTWEIGHT_MODELS = {
    'logistic_regression': {
        'max_iter': 100,
        'solver': 'liblinear',
        'random_state': 42
    },
    'decision_tree': {
        'max_depth': 10,
        'min_samples_split': 20,
        'random_state': 42
    },
    'naive_bayes': {
        'var_smoothing': 1e-9
    }
}

DEFAULT_MODEL = 'logistic_regression'

# ==================== FEDERATED LEARNING PARAMETERS ====================
# FedAvg parameters
CLIENT_PARTICIPATION_RATE = 0.8  # Fraction of clients participating per round
MIN_CLIENTS_PER_ROUND = 5

# Privacy and security
DIFFERENTIAL_PRIVACY = False  # Enable differential privacy
DP_EPSILON = 1.0
DP_DELTA = 1e-5

# Communication simulation
COMMUNICATION_DELAY_MS = [50, 100, 200, 500]  # Simulate network latency
BANDWIDTH_LIMIT_KBPS = [100, 500, 1000, 2000]  # Simulate bandwidth constraints

# ==================== EVALUATION METRICS ====================
METRICS_TO_TRACK = [
    'accuracy',
    'precision',
    'recall',
    'f1_score',
    'false_positive_rate',
    'detection_rate',
    'training_time',
    'inference_time',
    'memory_usage',
    'model_size',
    'communication_cost'
]

# ==================== ATTACK SIMULATION ====================
# Simulate different types of attacks for evaluation
ATTACK_TYPES = [
    'DDoS',
    'PortScan', 
    'Bot',
    'Web Attack',
    'DoS',
    'Infiltration'
]

# Real-time attack injection
ATTACK_INJECTION_RATE = 0.1  # 10% of real-time data will be attacks
ATTACK_PATTERN_EVOLUTION = True  # Simulate evolving attack patterns

# ==================== OUTPUT CONFIGURATION ====================
RESULTS_DIR = 'results'
LOGS_DIR = 'logs'
MODELS_DIR = 'models'
PLOTS_DIR = 'plots'

# Create directories if they don't exist
for directory in [RESULTS_DIR, LOGS_DIR, MODELS_DIR, PLOTS_DIR]:
    os.makedirs(directory, exist_ok=True)

# Logging configuration
LOG_LEVEL = 'INFO'  # DEBUG, INFO, WARNING, ERROR
DETAILED_LOGGING = True

# ==================== COMPARISON BASELINES ====================
# Compare with centralized approaches
ENABLE_CENTRALIZED_COMPARISON = True
CENTRALIZED_MODEL_TYPES = ['logistic_regression', 'random_forest', 'svm']

# ==================== VISUALIZATION ====================
ENABLE_REAL_TIME_PLOTS = True
PLOT_UPDATE_INTERVAL = 5  # seconds
SAVE_PLOTS = True
PLOT_FORMAT = 'png'  # png, pdf, svg
