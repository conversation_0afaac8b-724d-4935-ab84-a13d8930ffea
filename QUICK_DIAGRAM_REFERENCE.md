# Quick UML Diagrams Reference Guide

## 🚀 **Quick Start**

### **Files Overview**
- **`plantuml_diagrams.puml`** - Main diagrams (20+ diagrams)
- **`additional_uml_diagrams.puml`** - Specialized diagrams (5+ diagrams)
- **`UML_DIAGRAMS_DOCUMENTATION.md`** - Complete documentation

### **Rendering Options**

#### **Option 1: Online (Fastest)**
1. Go to: http://www.plantuml.com/plantuml/uml/
2. Copy any diagram code from `.puml` files
3. Paste and click "Submit"
4. Download PNG/SVG

#### **Option 2: VS Code (Recommended)**
1. Install "PlantUML" extension
2. Open `.puml` files
3. Press `Alt+D` to preview
4. Right-click → Export

#### **Option 3: Command Line**
```bash
java -jar plantuml.jar *.puml
```

## 📊 **Diagram Quick Reference**

### **🏗️ Architecture & Structure**

| Diagram | Purpose | Best For |
|---------|---------|----------|
| `system_architecture` | Overall system overview | Papers, presentations |
| `class_diagram` | OOP design details | Implementation docs |
| `component_diagram` | High-level components | System design |
| `deployment_diagram` | Physical deployment | Infrastructure planning |
| `package_diagram` | Code organization | Developer docs |

### **🔄 Behavior & Process**

| Diagram | Purpose | Best For |
|---------|---------|----------|
| `activity_diagram` | Federated learning workflow | Algorithm explanation |
| `sequence_diagram` | Component interactions | Implementation guide |
| `state_diagram` | Device state machine | Behavior specification |
| `federated_learning_process` | FedAvg algorithm | Research papers |

### **🤖 Model Structures**

| Diagram | Purpose | Best For |
|---------|---------|----------|
| `model_structure_logistic_regression` | LR model internals | ML methodology |
| `model_structure_decision_tree` | Tree model structure | Algorithm comparison |
| `model_structure_naive_bayes` | NB model structure | Probabilistic approach |

### **🔒 Security & Monitoring**

| Diagram | Purpose | Best For |
|---------|---------|----------|
| `security_architecture` | Security layers | Privacy discussion |
| `performance_monitoring` | Monitoring system | Evaluation framework |
| `network_topology` | Network layout | Deployment scenarios |

## 📝 **Research Paper Usage**

### **Methodology Section**
```latex
\begin{figure}[h]
\centering
\includegraphics[width=0.8\textwidth]{system_architecture.png}
\caption{Edge IDS Federated Learning System Architecture}
\label{fig:system_arch}
\end{figure}
```

### **Algorithm Description**
```latex
\begin{figure}[h]
\centering
\includegraphics[width=0.7\textwidth]{federated_learning_process.png}
\caption{FedAvg Algorithm Process Flow}
\label{fig:fedavg_process}
\end{figure}
```

### **Implementation Details**
```latex
\begin{figure}[h]
\centering
\includegraphics[width=0.9\textwidth]{class_diagram.png}
\caption{System Class Diagram and Component Relationships}
\label{fig:class_diagram}
\end{figure}
```

## 🎯 **Presentation Slides**

### **Slide Templates**

#### **System Overview Slide**
- **Title**: "Edge IDS Federated Learning Architecture"
- **Diagram**: `system_architecture`
- **Key Points**: Distributed processing, privacy preservation, scalability

#### **Algorithm Slide**
- **Title**: "Federated Averaging (FedAvg) Process"
- **Diagram**: `federated_learning_process`
- **Key Points**: Local training, secure aggregation, global model

#### **Security Slide**
- **Title**: "Privacy and Security Framework"
- **Diagram**: `security_architecture`
- **Key Points**: Data locality, differential privacy, secure communication

## 🔧 **Customization Tips**

### **Color Themes**
```plantuml
!theme plain          # Clean black/white
!theme bluegray       # Professional blue
!theme sketchy-outline # Hand-drawn style
!theme carbon         # Dark theme
```

### **Adding Your Logo**
```plantuml
!define LOGO_URL https://your-university.edu/logo.png
title <img:LOGO_URL> Your Research Title
```

### **Custom Colors**
```plantuml
skinparam backgroundColor #FFFFFF
skinparam componentBackgroundColor #E1F5FE
skinparam componentBorderColor #0277BD
```

## 📊 **Export Formats**

### **For Papers (High Quality)**
- **Format**: PNG (300 DPI) or SVG
- **Size**: Adjust width in LaTeX
- **Quality**: Vector graphics preferred

### **For Presentations**
- **Format**: PNG (150 DPI)
- **Size**: Slide-optimized
- **Background**: Transparent if needed

### **For Web/Documentation**
- **Format**: PNG (96 DPI) or SVG
- **Size**: Responsive
- **Compression**: Optimized for web

## 🎨 **Styling Guidelines**

### **Academic Papers**
```plantuml
!theme plain
skinparam monochrome true
skinparam shadowing false
skinparam backgroundColor white
```

### **Presentations**
```plantuml
!theme bluegray
skinparam backgroundColor #F5F5F5
skinparam roundCorner 10
```

### **Technical Documentation**
```plantuml
!theme carbon
skinparam backgroundColor #2B2B2B
skinparam defaultTextColor white
```

## 🚨 **Common Issues & Solutions**

### **Issue**: Diagram too large
**Solution**: Split into multiple diagrams or use `scale` directive
```plantuml
scale 0.8
```

### **Issue**: Text overlapping
**Solution**: Adjust spacing or use shorter labels
```plantuml
skinparam minClassWidth 100
skinparam nodesep 50
```

### **Issue**: Poor image quality
**Solution**: Use SVG format or increase DPI
```plantuml
!define DPI 300
```

## 📚 **Additional Resources**

### **PlantUML Documentation**
- Official Guide: https://plantuml.com/guide
- Syntax Reference: https://plantuml.com/sitemap-language-specification
- Examples Gallery: https://real-world-plantuml.com/

### **UML Standards**
- UML 2.5 Specification: https://www.omg.org/spec/UML/
- Best Practices: https://www.uml-diagrams.org/

### **Research Integration**
- LaTeX Integration: https://en.wikibooks.org/wiki/LaTeX/Importing_Graphics
- IEEE Paper Format: https://www.ieee.org/conferences/publishing/templates.html

---

**Quick Tip**: Start with `system_architecture` for overview, then use specific diagrams based on your research focus area!

**Total Available Diagrams**: 25+ comprehensive UML diagrams ready for your research! 🎉
