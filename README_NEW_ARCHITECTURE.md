# Edge IDS Federated Learning Simulation - New Architecture

## 🎯 **Research-Aligned Implementation**

This is a complete rework of the federated learning-based intrusion detection system (IDS) for edge computing environments, specifically designed to address your research objectives and methodology.

## 📋 **Research Objectives Addressed**

### 1. **Lightweight Design for Edge Constraints**
- ✅ Resource-constrained device simulation with different profiles (low/medium/high resource)
- ✅ Memory, CPU, and model size limitations enforced
- ✅ Lightweight machine learning models (Logistic Regression, Decision Trees, Naive Bayes)
- ✅ Real-time processing with minimal latency

### 2. **Federated Learning Architecture**
- ✅ Privacy-preserving federated learning (FedAvg algorithm)
- ✅ Local data remains on edge devices
- ✅ Only model updates are shared
- ✅ Differential privacy support
- ✅ Communication cost simulation

### 3. **Real-time Anomaly Detection**
- ✅ Streaming data processing simulation
- ✅ Real-time attack injection and detection
- ✅ Adaptive model updates through federated rounds
- ✅ Edge device failure simulation

### 4. **Comprehensive Evaluation**
- ✅ Comparison with centralized approaches
- ✅ Resource usage analysis (CPU, memory, latency)
- ✅ Detection accuracy, precision, recall, F1-score
- ✅ Communication overhead measurement
- ✅ Adaptability assessment

## 🏗️ **New Architecture Components**

### **Core Modules**

1. **`config.py`** - Centralized configuration
   - Edge device profiles and constraints
   - Federated learning parameters
   - Dataset and evaluation settings

2. **`edge_device.py`** - Edge Device Simulation
   - Resource-constrained device simulation
   - Local model training with timeouts
   - Real-time anomaly detection
   - Memory and model size monitoring

3. **`federated_server.py`** - Federated Learning Server
   - FedAvg algorithm implementation
   - Client selection strategies
   - Communication cost simulation
   - Differential privacy support

4. **`edge_data_processor.py`** - Lightweight Data Processing
   - Edge-focused data preprocessing
   - IID/Non-IID/Unbalanced data distribution
   - Real-time data streaming simulation
   - Attack injection for testing

5. **`edge_ids_simulation.py`** - Main Simulation Orchestrator
   - Complete simulation workflow
   - Real-time monitoring and visualization
   - Results collection and analysis

6. **`evaluation_framework.py`** - Comprehensive Evaluation
   - Federated vs centralized comparison
   - Resource efficiency analysis
   - Adaptability assessment
   - Research objectives evaluation

## 🚀 **Key Improvements Over Previous Implementation**

### **1. Edge-Focused Design**
- **Before**: Processing 2.8M samples (unrealistic for edge)
- **After**: Configurable dataset size (default 50K) for edge simulation

### **2. Resource Constraints**
- **Before**: No resource monitoring
- **After**: CPU, memory, model size, and training time limits

### **3. Real-time Simulation**
- **Before**: Batch processing only
- **After**: Streaming data with configurable batch sizes and intervals

### **4. Device Heterogeneity**
- **Before**: Identical devices
- **After**: Different device profiles (low/medium/high resource)

### **5. Privacy Preservation**
- **Before**: Basic model averaging
- **After**: Proper FedAvg with differential privacy options

### **6. Comprehensive Evaluation**
- **Before**: Basic accuracy metrics
- **After**: Multi-dimensional evaluation addressing all research objectives

## 📊 **Usage Instructions**

### **1. Installation**
```bash
pip install -r requirements.txt
```

### **2. Configuration**
Edit `config.py` to customize:
- Number of edge devices and FL rounds
- Device resource profiles
- Data distribution strategies
- Evaluation parameters

### **3. Run Simulation**
```bash
python edge_ids_simulation.py
```

### **4. Run with Evaluation**
```python
from edge_ids_simulation import EdgeIDSSimulation
from evaluation_framework import EvaluationFramework

# Run simulation
simulation = EdgeIDSSimulation()
simulation.initialize_simulation()
simulation.run_federated_learning()

# Comprehensive evaluation
evaluator = EvaluationFramework()
report = evaluator.generate_comprehensive_report(simulation.simulation_results)
evaluator.save_evaluation_report(report)
```

## 📈 **Expected Outputs**

### **1. Real-time Monitoring**
- Live plots of accuracy, F1-score, communication costs
- Device participation tracking
- Resource usage visualization

### **2. Simulation Results**
- JSON files with complete simulation data
- Round-by-round metrics
- Device-specific performance data

### **3. Evaluation Reports**
- Federated vs centralized comparison
- Resource efficiency analysis
- Adaptability assessment
- Research objectives evaluation

## 🔬 **Research Contributions**

### **1. Lightweight IDS Design**
- Demonstrates feasibility of ML-based IDS on resource-constrained edge devices
- Quantifies resource requirements and constraints

### **2. Privacy-Preserving Federated Learning**
- Shows how federated learning preserves data privacy
- Measures communication overhead vs centralized approaches

### **3. Real-time Adaptability**
- Evaluates system's ability to adapt to evolving threats
- Measures convergence speed and stability

### **4. Comprehensive Performance Analysis**
- Multi-dimensional evaluation framework
- Comparison with traditional centralized approaches
- Resource efficiency quantification

## 📋 **Configuration Examples**

### **Edge Device Profiles**
```python
EDGE_DEVICE_PROFILES = {
    'low_resource': {
        'cpu_cores': 1,
        'memory_mb': 512,
        'max_model_size_mb': 5,
        'max_training_samples': 1000,
        'training_time_limit': 30,
        'count': 4
    },
    # ... more profiles
}
```

### **Data Distribution Strategies**
```python
DATA_DISTRIBUTION = 'non_iid'  # 'iid', 'non_iid', 'unbalanced'
```

### **Real-time Simulation**
```python
REAL_TIME_SIMULATION = True
STREAM_BATCH_SIZE = 100
STREAM_INTERVAL = 2.0  # seconds
```

## 🎯 **Research Validation**

This implementation directly addresses your research methodology:

1. **✅ Simulation-based experimental approach**
2. **✅ Multiple edge devices with resource constraints**
3. **✅ Lightweight ML models for anomaly detection**
4. **✅ Federated learning without raw data sharing**
5. **✅ Performance evaluation with multiple metrics**
6. **✅ Comparison with centralized approaches**

## 📊 **Expected Research Outcomes**

### **Performance Metrics**
- Detection accuracy: 85-95%
- False positive rate: <5%
- Training time: <60s per device
- Inference time: <100ms
- Model size: <10MB

### **Resource Efficiency**
- Memory usage: <512MB per device
- Communication overhead: 50-80% reduction vs centralized
- Energy efficiency through local processing

### **Privacy Benefits**
- No raw data transmission
- Differential privacy protection
- Reduced attack surface

## 🔄 **Next Steps**

1. **Run the simulation** with your dataset
2. **Analyze results** using the evaluation framework
3. **Tune parameters** based on your specific requirements
4. **Generate research outputs** from comprehensive evaluation reports

This architecture provides a solid foundation for your research on lightweight, federated learning-based IDS for edge computing environments!
