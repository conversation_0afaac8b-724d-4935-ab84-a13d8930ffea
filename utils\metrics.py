import numpy as np
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns

def evaluate_model(model, X_test, y_test, client_id="Global"):
    """
    Evaluates a given machine learning model on test data and prints key metrics.

    Args:
        model: The trained machine learning model.
        X_test (pd.DataFrame or np.array): Test features.
        y_test (pd.Series or np.array): True test labels.
        client_id (str): Identifier for the client or "Global" for the aggregated model.
    
    Returns:
        tuple: A tuple containing (accuracy, false_positive_rate, confusion_matrix).
    """
    if X_test.empty or y_test.empty:
        print(f"Warning: No test data available for {client_id} evaluation.")
        return 0.0, 0.0, np.array([[0,0],[0,0]]) # Return zeros for empty data

    print(f"\n--- Evaluating Model for {client_id} ---")
    y_pred = model.predict(X_test)

    accuracy = accuracy_score(y_test, y_pred)
    print(f"Accuracy: {accuracy:.4f}")

    cm = confusion_matrix(y_test, y_pred)
    print("Confusion Matrix:")
    print(cm)

    # Calculate False Positive Rate (FPR)
    # FPR = FP / (FP + TN)
    # Assuming a binary classification problem: 0 for benign, 1 for attack
    # If your labels are different, adjust the interpretation.
    # For a multi-class problem, this calculation needs adjustment per class.

    tn, fp, fn, tp = 0, 0, 0, 0
    if cm.shape == (2,2): # Binary classification
        tn, fp, fn, tp = cm.ravel()
        if (fp + tn) > 0:
            false_positive_rate = fp / (fp + tn)
        else:
            false_positive_rate = 0.0
        print(f"False Positive Rate (FPR): {false_positive_rate:.4f}")
    else: # Multi-class classification
        # For multi-class, FPR is typically calculated per class or averaged.
        # We'll simplify and report general metrics for now.
        false_positive_rate = -1 # Indicate not calculated for multi-class
        print("Multi-class classification. FPR calculation is more complex per class.")
        
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred, zero_division=0))

    return accuracy, false_positive_rate, cm

def plot_metrics(accuracies, fprs, rounds):
    """
    Plots the accuracy and false positive rate over training rounds.

    Args:
        accuracies (list): List of global accuracies for each round.
        fprs (list): List of global false positive rates for each round.
        rounds (int): Number of federated learning rounds.
    """
    if not accuracies and not fprs:
        print("No metrics to plot.")
        return

    plt.style.use('seaborn-v0_8-darkgrid') # Modern seaborn style
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    fig.suptitle('Federated Learning Performance Over Rounds', fontsize=16)

    # Accuracy Plot
    ax1.plot(range(1, rounds + 1), accuracies, marker='o', linestyle='-', color='#1f77b4', linewidth=2)
    ax1.set_title('Global Model Accuracy per Round', fontsize=14)
    ax1.set_xlabel('Federated Round', fontsize=12)
    ax1.set_ylabel('Accuracy', fontsize=12)
    ax1.set_xticks(range(1, rounds + 1))
    ax1.set_ylim(0, 1) # Accuracy always between 0 and 1
    ax1.grid(True, linestyle='--', alpha=0.7)
    for i, acc in enumerate(accuracies):
        ax1.text(i + 1, acc, f'{acc:.2f}', ha='center', va='bottom')


    # FPR Plot
    ax2.plot(range(1, rounds + 1), fprs, marker='s', linestyle='--', color='#ff7f0e', linewidth=2)
    ax2.set_title('Global Model False Positive Rate (FPR) per Round', fontsize=14)
    ax2.set_xlabel('Federated Round', fontsize=12)
    ax2.set_ylabel('FPR', fontsize=12)
    ax2.set_xticks(range(1, rounds + 1))
    ax2.set_ylim(0, 1) # FPR always between 0 and 1
    ax2.grid(True, linestyle='--', alpha=0.7)
    for i, fpr in enumerate(fprs):
        ax2.text(i + 1, fpr, f'{fpr:.2f}', ha='center', va='top')

    plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout to prevent title overlap
    plt.show()

