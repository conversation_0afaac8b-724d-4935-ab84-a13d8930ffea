"""
Edge Device Simulation Module
Simulates individual edge devices with resource constraints and local IDS capabilities.
"""

import time
import psutil
import numpy as np
import pandas as pd
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import pickle
import threading
from datetime import datetime
import logging
from config import *

class EdgeDevice:
    """
    Simulates an edge device with resource constraints and local IDS capabilities.
    """
    
    def __init__(self, device_id, device_profile, model_type='logistic_regression'):
        self.device_id = device_id
        self.profile = device_profile
        self.model_type = model_type
        
        # Resource constraints
        self.cpu_cores = device_profile['cpu_cores']
        self.memory_mb = device_profile['memory_mb']
        self.max_model_size_mb = device_profile['max_model_size_mb']
        self.max_training_samples = device_profile['max_training_samples']
        self.training_time_limit = device_profile['training_time_limit']
        
        # Local model
        self.local_model = self._initialize_model()
        self.is_trained = False
        
        # Data storage
        self.local_data = None
        self.local_labels = None
        
        # Performance metrics
        self.metrics = {
            'training_time': 0,
            'inference_time': 0,
            'memory_usage': 0,
            'model_size': 0,
            'accuracy': 0,
            'precision': 0,
            'recall': 0,
            'f1_score': 0,
            'samples_processed': 0
        }
        
        # Real-time processing
        self.is_processing = False
        self.detection_buffer = []
        
        # Logging
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup device-specific logging."""
        logger = logging.getLogger(f'EdgeDevice_{self.device_id}')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(f'{LOGS_DIR}/device_{self.device_id}.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _initialize_model(self):
        """Initialize lightweight model based on device capabilities."""
        if self.model_type == 'logistic_regression':
            return LogisticRegression(**LIGHTWEIGHT_MODELS['logistic_regression'])
        elif self.model_type == 'decision_tree':
            return DecisionTreeClassifier(**LIGHTWEIGHT_MODELS['decision_tree'])
        elif self.model_type == 'naive_bayes':
            return GaussianNB(**LIGHTWEIGHT_MODELS['naive_bayes'])
        else:
            return LogisticRegression(**LIGHTWEIGHT_MODELS['logistic_regression'])
    
    def load_local_data(self, X_data, y_data):
        """
        Load local training data with resource constraints.
        Simulates edge device receiving limited local data.
        """
        # Limit data based on device constraints
        max_samples = min(len(X_data), self.max_training_samples)
        
        if len(X_data) > max_samples:
            # Randomly sample data to fit device constraints
            indices = np.random.choice(len(X_data), max_samples, replace=False)
            self.local_data = X_data.iloc[indices] if isinstance(X_data, pd.DataFrame) else X_data[indices]
            self.local_labels = y_data.iloc[indices] if isinstance(y_data, pd.Series) else y_data[indices]
        else:
            self.local_data = X_data
            self.local_labels = y_data
        
        self.logger.info(f"Loaded {len(self.local_data)} samples (max: {max_samples})")
        
    def train_local_model(self, global_model_weights=None):
        """
        Train local model with resource constraints and time limits.
        """
        if self.local_data is None:
            self.logger.warning("No local data available for training")
            return False
        
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        try:
            # Initialize model with global weights if available
            if global_model_weights is not None:
                self._load_global_weights(global_model_weights)
            
            # Train with time limit
            training_thread = threading.Thread(target=self._train_with_timeout)
            training_thread.start()
            training_thread.join(timeout=self.training_time_limit)
            
            if training_thread.is_alive():
                self.logger.warning(f"Training timeout ({self.training_time_limit}s) reached")
                return False
            
            # Calculate metrics
            training_time = time.time() - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_usage = end_memory - start_memory
            
            # Model size estimation
            model_size = self._estimate_model_size()
            
            # Check resource constraints
            if model_size > self.max_model_size_mb:
                self.logger.warning(f"Model size ({model_size:.2f}MB) exceeds limit ({self.max_model_size_mb}MB)")
                return False
            
            # Update metrics
            self.metrics.update({
                'training_time': training_time,
                'memory_usage': memory_usage,
                'model_size': model_size,
                'samples_processed': len(self.local_data)
            })
            
            self.is_trained = True
            self.logger.info(f"Training completed in {training_time:.2f}s, model size: {model_size:.2f}MB")
            return True
            
        except Exception as e:
            self.logger.error(f"Training failed: {str(e)}")
            return False
    
    def _train_with_timeout(self):
        """Train model in separate thread to enable timeout."""
        try:
            self.local_model.fit(self.local_data, self.local_labels)
        except Exception as e:
            self.logger.error(f"Model training error: {str(e)}")
    
    def _load_global_weights(self, global_weights):
        """Load global model weights for federated learning."""
        try:
            if hasattr(self.local_model, 'coef_') and 'coef_' in global_weights:
                self.local_model.coef_ = global_weights['coef_']
            if hasattr(self.local_model, 'intercept_') and 'intercept_' in global_weights:
                self.local_model.intercept_ = global_weights['intercept_']
            if hasattr(self.local_model, 'classes_') and 'classes_' in global_weights:
                self.local_model.classes_ = global_weights['classes_']
        except Exception as e:
            self.logger.warning(f"Failed to load global weights: {str(e)}")
    
    def get_model_weights(self):
        """Extract model weights for federated aggregation."""
        if not self.is_trained:
            return None
        
        weights = {}
        try:
            if hasattr(self.local_model, 'coef_'):
                weights['coef_'] = self.local_model.coef_.copy()
            if hasattr(self.local_model, 'intercept_'):
                weights['intercept_'] = self.local_model.intercept_.copy()
            if hasattr(self.local_model, 'classes_'):
                weights['classes_'] = self.local_model.classes_.copy()
            
            # Add metadata
            weights['num_samples'] = len(self.local_data)
            weights['device_id'] = self.device_id
            
        except Exception as e:
            self.logger.error(f"Failed to extract weights: {str(e)}")
            return None
        
        return weights
    
    def real_time_detection(self, data_batch):
        """
        Perform real-time anomaly detection on incoming data batch.
        Simulates edge device processing network traffic in real-time.
        """
        if not self.is_trained:
            self.logger.warning("Model not trained, cannot perform detection")
            return None
        
        start_time = time.time()
        
        try:
            # Predict anomalies
            predictions = self.local_model.predict(data_batch)
            probabilities = None
            
            if hasattr(self.local_model, 'predict_proba'):
                probabilities = self.local_model.predict_proba(data_batch)
            
            inference_time = time.time() - start_time
            self.metrics['inference_time'] = inference_time
            
            # Store detections for analysis
            detection_result = {
                'timestamp': datetime.now(),
                'predictions': predictions,
                'probabilities': probabilities,
                'inference_time': inference_time,
                'batch_size': len(data_batch)
            }
            
            self.detection_buffer.append(detection_result)
            
            # Keep buffer size manageable
            if len(self.detection_buffer) > 100:
                self.detection_buffer.pop(0)
            
            return detection_result
            
        except Exception as e:
            self.logger.error(f"Real-time detection failed: {str(e)}")
            return None
    
    def evaluate_performance(self, X_test, y_test):
        """Evaluate model performance on test data."""
        if not self.is_trained:
            return None
        
        try:
            y_pred = self.local_model.predict(X_test)
            
            metrics = {
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0)
            }
            
            self.metrics.update(metrics)
            return metrics
            
        except Exception as e:
            self.logger.error(f"Evaluation failed: {str(e)}")
            return None
    
    def _estimate_model_size(self):
        """Estimate model size in MB."""
        try:
            # Serialize model to estimate size
            model_bytes = pickle.dumps(self.local_model)
            size_mb = len(model_bytes) / (1024 * 1024)
            return size_mb
        except:
            return 0.1  # Default small size
    
    def get_device_status(self):
        """Get current device status and metrics."""
        return {
            'device_id': self.device_id,
            'profile': self.profile,
            'is_trained': self.is_trained,
            'is_processing': self.is_processing,
            'metrics': self.metrics.copy(),
            'data_samples': len(self.local_data) if self.local_data is not None else 0
        }
    
    def simulate_device_failure(self, failure_probability=0.05):
        """Simulate device failures (network issues, hardware problems)."""
        return np.random.random() < failure_probability

    def cleanup(self):
        """Cleanup device resources."""
        self.detection_buffer.clear()
        if hasattr(self, 'logger'):
            for handler in self.logger.handlers:
                handler.close()
                self.logger.removeHandler(handler)
