2025-08-18 13:14:44,867 - FederatedServer - WARNING - Only 3 healthy devices available
2025-08-18 13:22:23,364 - FederatedServer - INFO - Starting federated learning round 1
2025-08-18 13:22:23,364 - FederatedServer - INFO - Selected 8 devices for round 2
2025-08-18 13:22:24,584 - FederatedServer - INFO - Model distribution completed in 1.22s
2025-08-18 13:22:26,333 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:26,334 - FederatedServer - ERROR - Model aggregation failed: non-broadcastable output operand with shape (1,78) doesn't match the broadcast shape (5,78)
2025-08-18 13:22:26,334 - FederatedServer - INFO - Round 1 completed in 2.97s
2025-08-18 13:22:27,335 - FederatedServer - INFO - Starting federated learning round 2
2025-08-18 13:22:27,336 - FederatedServer - INFO - Selected 8 devices for round 3
2025-08-18 13:22:28,490 - FederatedServer - INFO - Model distribution completed in 1.15s
2025-08-18 13:22:30,195 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:30,195 - FederatedServer - ERROR - Model aggregation failed: non-broadcastable output operand with shape (1,78) doesn't match the broadcast shape (6,78)
2025-08-18 13:22:30,195 - FederatedServer - INFO - Round 2 completed in 2.86s
2025-08-18 13:22:31,199 - FederatedServer - INFO - Starting federated learning round 3
2025-08-18 13:22:31,199 - FederatedServer - INFO - Selected 8 devices for round 4
2025-08-18 13:22:32,153 - FederatedServer - INFO - Model distribution completed in 0.95s
2025-08-18 13:22:33,692 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:33,693 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (7,78) (6,78) (7,78) 
2025-08-18 13:22:33,693 - FederatedServer - INFO - Round 3 completed in 2.49s
2025-08-18 13:22:34,695 - FederatedServer - INFO - Starting federated learning round 4
2025-08-18 13:22:34,695 - FederatedServer - INFO - Selected 8 devices for round 5
2025-08-18 13:22:37,000 - FederatedServer - INFO - Model distribution completed in 2.30s
2025-08-18 13:22:38,689 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:38,689 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (7,78) (5,78) 
2025-08-18 13:22:38,689 - FederatedServer - INFO - Round 4 completed in 3.99s
2025-08-18 13:22:39,692 - FederatedServer - INFO - Starting federated learning round 5
2025-08-18 13:22:39,692 - FederatedServer - INFO - Selected 8 devices for round 6
2025-08-18 13:22:41,858 - FederatedServer - INFO - Model distribution completed in 2.17s
2025-08-18 13:22:42,776 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:42,776 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (6,78) (5,78) 
2025-08-18 13:22:42,776 - FederatedServer - INFO - Round 5 completed in 3.08s
2025-08-18 13:22:43,777 - FederatedServer - INFO - Starting federated learning round 6
2025-08-18 13:22:43,778 - FederatedServer - INFO - Selected 8 devices for round 7
2025-08-18 13:22:45,632 - FederatedServer - INFO - Model distribution completed in 1.85s
2025-08-18 13:22:47,782 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:47,782 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (6,78) (5,78) 
2025-08-18 13:22:47,783 - FederatedServer - INFO - Round 6 completed in 4.01s
2025-08-18 13:22:48,785 - FederatedServer - INFO - Starting federated learning round 7
2025-08-18 13:22:48,785 - FederatedServer - INFO - Selected 8 devices for round 8
2025-08-18 13:22:50,090 - FederatedServer - INFO - Model distribution completed in 1.30s
2025-08-18 13:22:51,607 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:51,607 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (7,78) (5,78) 
2025-08-18 13:22:51,607 - FederatedServer - INFO - Round 7 completed in 2.82s
2025-08-18 13:22:52,612 - FederatedServer - INFO - Starting federated learning round 8
2025-08-18 13:22:52,620 - FederatedServer - INFO - Selected 8 devices for round 9
2025-08-18 13:22:53,874 - FederatedServer - INFO - Model distribution completed in 1.25s
2025-08-18 13:22:55,537 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:55,537 - FederatedServer - ERROR - Model aggregation failed: non-broadcastable output operand with shape (1,78) doesn't match the broadcast shape (7,78)
2025-08-18 13:22:55,537 - FederatedServer - INFO - Round 8 completed in 2.92s
2025-08-18 13:22:56,538 - FederatedServer - INFO - Starting federated learning round 9
2025-08-18 13:22:56,538 - FederatedServer - INFO - Selected 8 devices for round 10
2025-08-18 13:22:57,643 - FederatedServer - INFO - Model distribution completed in 1.10s
2025-08-18 13:22:59,966 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:22:59,966 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (4,78) (5,78) 
2025-08-18 13:22:59,966 - FederatedServer - INFO - Round 9 completed in 3.43s
2025-08-18 13:23:00,968 - FederatedServer - INFO - Starting federated learning round 10
2025-08-18 13:23:00,968 - FederatedServer - INFO - Selected 8 devices for round 11
2025-08-18 13:23:01,921 - FederatedServer - INFO - Model distribution completed in 0.95s
2025-08-18 13:23:04,057 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:04,057 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (7,78) (5,78) 
2025-08-18 13:23:04,057 - FederatedServer - INFO - Round 10 completed in 3.09s
2025-08-18 13:23:05,058 - FederatedServer - INFO - Starting federated learning round 11
2025-08-18 13:23:05,059 - FederatedServer - INFO - Selected 8 devices for round 12
2025-08-18 13:23:06,913 - FederatedServer - INFO - Model distribution completed in 1.85s
2025-08-18 13:23:08,274 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:08,274 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (4,78) (5,78) 
2025-08-18 13:23:08,274 - FederatedServer - INFO - Round 11 completed in 3.22s
2025-08-18 13:23:09,277 - FederatedServer - INFO - Starting federated learning round 12
2025-08-18 13:23:09,278 - FederatedServer - INFO - Selected 8 devices for round 13
2025-08-18 13:23:11,134 - FederatedServer - INFO - Model distribution completed in 1.86s
2025-08-18 13:23:12,775 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:12,775 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (7,78) (5,78) 
2025-08-18 13:23:12,775 - FederatedServer - INFO - Round 12 completed in 3.50s
2025-08-18 13:23:13,776 - FederatedServer - INFO - Starting federated learning round 13
2025-08-18 13:23:13,776 - FederatedServer - INFO - Selected 8 devices for round 14
2025-08-18 13:23:15,647 - FederatedServer - INFO - Model distribution completed in 1.87s
2025-08-18 13:23:17,241 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:17,241 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (7,78) (5,78) (7,78) 
2025-08-18 13:23:17,241 - FederatedServer - INFO - Round 13 completed in 3.46s
2025-08-18 13:23:18,242 - FederatedServer - INFO - Starting federated learning round 14
2025-08-18 13:23:18,242 - FederatedServer - INFO - Selected 8 devices for round 15
2025-08-18 13:23:20,578 - FederatedServer - INFO - Model distribution completed in 2.34s
2025-08-18 13:23:22,240 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:22,240 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (4,78) (5,78) (4,78) 
2025-08-18 13:23:22,240 - FederatedServer - INFO - Round 14 completed in 4.00s
2025-08-18 13:23:23,242 - FederatedServer - INFO - Starting federated learning round 15
2025-08-18 13:23:23,242 - FederatedServer - INFO - Selected 8 devices for round 16
2025-08-18 13:23:24,945 - FederatedServer - INFO - Model distribution completed in 1.70s
2025-08-18 13:23:26,448 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:26,449 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (4,78) (5,78) (4,78) 
2025-08-18 13:23:26,449 - FederatedServer - INFO - Round 15 completed in 3.21s
2025-08-18 13:23:27,451 - FederatedServer - INFO - Starting federated learning round 16
2025-08-18 13:23:27,451 - FederatedServer - INFO - Selected 8 devices for round 17
2025-08-18 13:23:28,805 - FederatedServer - INFO - Model distribution completed in 1.35s
2025-08-18 13:23:31,112 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:31,116 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (4,78) (6,78) (4,78) 
2025-08-18 13:23:31,117 - FederatedServer - INFO - Round 16 completed in 3.67s
2025-08-18 13:23:32,789 - FederatedServer - INFO - Starting federated learning round 17
2025-08-18 13:23:32,789 - FederatedServer - INFO - Selected 8 devices for round 18
2025-08-18 13:23:34,493 - FederatedServer - INFO - Model distribution completed in 1.70s
2025-08-18 13:23:36,404 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:36,404 - FederatedServer - ERROR - Model aggregation failed: operands could not be broadcast together with shapes (5,78) (4,78) (5,78) 
2025-08-18 13:23:36,404 - FederatedServer - INFO - Round 17 completed in 3.62s
2025-08-18 13:23:37,683 - FederatedServer - INFO - Starting federated learning round 18
2025-08-18 13:23:37,683 - FederatedServer - INFO - Selected 8 devices for round 19
2025-08-18 13:23:39,437 - FederatedServer - INFO - Model distribution completed in 1.75s
2025-08-18 13:23:41,620 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:41,621 - FederatedServer - ERROR - Model aggregation failed: non-broadcastable output operand with shape (1,78) doesn't match the broadcast shape (6,78)
2025-08-18 13:23:41,621 - FederatedServer - INFO - Round 18 completed in 3.94s
2025-08-18 13:23:42,656 - FederatedServer - INFO - Starting federated learning round 19
2025-08-18 13:23:42,657 - FederatedServer - INFO - Selected 8 devices for round 20
2025-08-18 13:23:44,018 - FederatedServer - INFO - Model distribution completed in 1.36s
2025-08-18 13:23:45,859 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:45,860 - FederatedServer - ERROR - Model aggregation failed: non-broadcastable output operand with shape (1,78) doesn't match the broadcast shape (7,78)
2025-08-18 13:23:45,860 - FederatedServer - INFO - Round 19 completed in 3.20s
2025-08-18 13:23:46,862 - FederatedServer - INFO - Starting federated learning round 20
2025-08-18 13:23:46,862 - FederatedServer - INFO - Selected 8 devices for round 21
2025-08-18 13:23:48,713 - FederatedServer - INFO - Model distribution completed in 1.85s
2025-08-18 13:23:50,511 - FederatedServer - INFO - Collected 8 model updates
2025-08-18 13:23:50,511 - FederatedServer - ERROR - Model aggregation failed: non-broadcastable output operand with shape (1,78) doesn't match the broadcast shape (7,78)
2025-08-18 13:23:50,511 - FederatedServer - INFO - Round 20 completed in 3.65s
