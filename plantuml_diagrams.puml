@startuml system_architecture
!theme plain
title Edge IDS Federated Learning - System Architecture

package "Edge Computing Environment" {
    
    package "Edge Device 1" as ED1 {
        component [Local Data] as LD1
        component [Edge IDS Model] as EIM1
        component [Local Training] as LT1
        component [Anomaly Detection] as AD1
        
        LD1 --> LT1 : Train
        LT1 --> EIM1 : Update
        EIM1 --> AD1 : Detect
    }
    
    package "Edge Device 2" as ED2 {
        component [Local Data] as LD2
        component [Edge IDS Model] as EIM2
        component [Local Training] as LT2
        component [Anomaly Detection] as AD2
        
        LD2 --> LT2 : Train
        LT2 --> EIM2 : Update
        EIM2 --> AD2 : Detect
    }
    
    package "Edge Device N" as EDN {
        component [Local Data] as LDN
        component [Edge IDS Model] as EIMN
        component [Local Training] as LTN
        component [Anomaly Detection] as ADN
        
        LDN --> LTN : Train
        LTN --> EIMN : Update
        EIMN --> ADN : Detect
    }
}

package "Federated Learning Server" {
    component [Global Model] as GM
    component [FedAvg Aggregator] as FA
    component [Client Selection] as CS
    component [Model Distribution] as MD
    
    CS --> MD : Select Clients
    FA --> GM : Update Global
    GM --> MD : Distribute
}

package "Network Infrastructure" {
    component [Communication Layer] as CL
    component [Privacy Preservation] as PP
    component [Differential Privacy] as DP
    
    CL --> PP : Secure
    PP --> DP : Apply
}

package "Monitoring & Evaluation" {
    component [Performance Monitor] as PM
    component [Resource Tracker] as RT
    component [Evaluation Framework] as EF
    
    PM --> EF : Metrics
    RT --> EF : Resources
}

' Connections
ED1 -.-> CL : Model Updates
ED2 -.-> CL : Model Updates
EDN -.-> CL : Model Updates

CL --> FA : Aggregated Updates
MD -.-> ED1 : Global Model
MD -.-> ED2 : Global Model
MD -.-> EDN : Global Model

PM --> ED1 : Monitor
PM --> ED2 : Monitor
PM --> EDN : Monitor

note right of GM : "Privacy-preserving\nfederated learning\nwith FedAvg"

note left of ED1 : "Resource-constrained\nedge devices with\nlocal data"

@enduml

@startuml class_diagram
!theme plain
title Edge IDS Federated Learning - Class Diagram

package "Core Components" {
    
    class EdgeDevice {
        - device_id: int
        - device_profile: dict
        - local_model: LogisticRegression
        - is_healthy: bool
        - resource_monitor: ResourceMonitor
        + __init__(device_id, device_profile)
        + load_local_data(X_train, y_train)
        + train_local_model(): bool
        + get_model_weights(): dict
        + update_global_model(weights)
        + real_time_detection(X_batch): dict
        + check_resource_constraints(): bool
        + cleanup()
    }
    
    class FederatedServer {
        - participating_devices: list
        - global_model: LogisticRegression
        - aggregation_weights: dict
        + __init__()
        + select_participating_devices(devices): list
        + coordinate_training_round(devices): dict
        + aggregate_model_updates(updates): dict
        + distribute_global_model(devices)
        + apply_differential_privacy(updates): dict
        + calculate_communication_cost(): float
    }
    
    class EdgeDataProcessor {
        - label_encoder: LabelEncoder
        - scaler: StandardScaler
        + __init__()
        + load_and_prepare_edge_dataset(): tuple
        + create_federated_data_splits(X, y, num_devices): dict
        + create_iid_distribution(X, y, num_devices): dict
        + create_non_iid_distribution(X, y, num_devices): dict
        + create_unbalanced_distribution(X, y, num_devices): dict
        + create_real_time_stream(X, y): Generator
        + inject_attacks(X_batch, attack_ratio): tuple
    }
    
    class EdgeIDSSimulation {
        - devices: list[EdgeDevice]
        - server: FederatedServer
        - data_processor: EdgeDataProcessor
        - simulation_results: dict
        - is_running: bool
        + __init__()
        + initialize_simulation()
        + run_federated_learning()
        + simulate_real_time_detection()
        + collect_performance_metrics(): dict
        + save_simulation_results()
    }
    
    class EvaluationFramework {
        - data_processor: EdgeDataProcessor
        - evaluation_results: dict
        + __init__()
        + evaluate_federated_vs_centralized(results, test_data): dict
        + evaluate_resource_efficiency(results): dict
        + evaluate_adaptability(results): dict
        + generate_comprehensive_report(results): dict
        + save_evaluation_report(report, filename)
    }
}

package "Utility Classes" {
    
    class ResourceMonitor {
        - cpu_usage: float
        - memory_usage: float
        - model_size: float
        + __init__()
        + get_cpu_usage(): float
        + get_memory_usage(): float
        + get_model_size(model): float
        + check_constraints(profile): bool
    }
    
    class ConfigManager {
        + NUM_EDGE_DEVICES: int
        + FL_ROUNDS: int
        + EDGE_DEVICE_PROFILES: dict
        + LIGHTWEIGHT_MODELS: dict
        + DATA_DISTRIBUTION: str
        + ENABLE_REAL_TIME_PLOTS: bool
    }
    
    class Logger {
        - logger: logging.Logger
        + __init__(name: str)
        + info(message: str)
        + warning(message: str)
        + error(message: str)
    }
}

' Relationships
EdgeIDSSimulation *-- EdgeDevice : contains
EdgeIDSSimulation *-- FederatedServer : contains
EdgeIDSSimulation *-- EdgeDataProcessor : contains
EdgeDevice *-- ResourceMonitor : uses
EdgeDevice ..> ConfigManager : reads config
FederatedServer ..> ConfigManager : reads config
EdgeDataProcessor ..> ConfigManager : reads config
EvaluationFramework *-- EdgeDataProcessor : uses
EdgeDevice ..> Logger : logs to
FederatedServer ..> Logger : logs to
EdgeIDSSimulation ..> Logger : logs to

@enduml

@startuml activity_diagram
!theme plain
title Federated Learning IDS - Activity Flow

start

:Initialize Edge Devices;
note right: Create devices with\nresource constraints

:Load and Distribute Data;
note right: Non-IID distribution\nacross edge devices

:Initialize Global Model;

repeat :Federated Learning Round;
    
    :Select Participating Devices;
    note right: Based on health\nand availability
    
    fork
        :Device 1: Local Training;
        :Extract Model Weights;
    fork again
        :Device 2: Local Training;
        :Extract Model Weights;
    fork again
        :Device N: Local Training;
        :Extract Model Weights;
    end fork
    
    :Collect Model Updates;
    
    :Apply Differential Privacy;
    note right: Optional privacy\nenhancement
    
    :Aggregate Updates (FedAvg);
    note right: Weighted averaging\nof model parameters
    
    :Update Global Model;
    
    :Distribute Global Model;
    
    :Evaluate Performance;
    note right: Accuracy, F1-score,\nResource usage
    
    :Log Round Results;

repeat while (Round < MAX_ROUNDS?)

:Generate Final Evaluation;

fork
    :Performance Analysis;
    note right: Accuracy trends,\nConfusion matrices
fork again
    :Resource Efficiency;
    note right: CPU, Memory,\nCommunication costs
fork again
    :Comparison Analysis;
    note right: Federated vs\nCentralized approaches
end fork

:Save Results and Reports;

stop

@enduml

@startuml sequence_diagram
!theme plain
title Federated Learning Round - Sequence Diagram

participant "Simulation Controller" as SC
participant "Federated Server" as FS
participant "Edge Device 1" as ED1
participant "Edge Device 2" as ED2
participant "Resource Monitor" as RM
participant "Evaluation Framework" as EF

SC -> FS: start_federated_round()
activate FS

FS -> FS: select_participating_devices()
FS -> ED1: check_device_health()
activate ED1
ED1 -> RM: get_resource_status()
activate RM
RM --> ED1: resource_status
deactivate RM
ED1 --> FS: health_status
deactivate ED1

FS -> ED2: check_device_health()
activate ED2
ED2 -> RM: get_resource_status()
activate RM
RM --> ED2: resource_status
deactivate RM
ED2 --> FS: health_status
deactivate ED2

FS -> ED1: distribute_global_model(weights)
activate ED1
FS -> ED2: distribute_global_model(weights)
activate ED2

ED1 -> ED1: train_local_model()
ED2 -> ED2: train_local_model()

ED1 -> RM: monitor_training_resources()
activate RM
RM --> ED1: resource_metrics
deactivate RM

ED2 -> RM: monitor_training_resources()
activate RM
RM --> ED2: resource_metrics
deactivate RM

ED1 --> FS: local_model_weights
deactivate ED1
ED2 --> FS: local_model_weights
deactivate ED2

FS -> FS: aggregate_model_updates()
FS -> FS: update_global_model()

FS --> SC: round_results
deactivate FS

SC -> EF: evaluate_round_performance(results)
activate EF
EF -> EF: calculate_metrics()
EF --> SC: performance_metrics
deactivate EF

SC -> SC: log_round_results()

@enduml

@startuml component_diagram
!theme plain
title Edge IDS System - Component Diagram

package "Edge Computing Layer" {
    [Edge Device Manager] as EDM
    [Local Data Storage] as LDS
    [Resource Monitor] as RM
    [Anomaly Detector] as AD

    interface "IDeviceManager" as IDM
    interface "IResourceMonitor" as IRM
    interface "IAnomalyDetector" as IAD

    EDM - IDM
    RM - IRM
    AD - IAD
}

package "Federated Learning Layer" {
    [Federated Server] as FS
    [Model Aggregator] as MA
    [Client Selector] as CS
    [Global Model Manager] as GMM

    interface "IFederatedServer" as IFS
    interface "IModelAggregator" as IMA
    interface "IClientSelector" as ICS

    FS - IFS
    MA - IMA
    CS - ICS
}

package "Data Processing Layer" {
    [Data Preprocessor] as DP
    [Feature Extractor] as FE
    [Data Distributor] as DD
    [Stream Processor] as SP

    interface "IDataProcessor" as IDP
    interface "IFeatureExtractor" as IFE
    interface "IDataDistributor" as IDD

    DP - IDP
    FE - IFE
    DD - IDD
}

package "Machine Learning Layer" {
    [Logistic Regression Model] as LRM
    [Decision Tree Model] as DTM
    [Naive Bayes Model] as NBM
    [Model Trainer] as MT

    interface "IMLModel" as IML
    interface "IModelTrainer" as IMT

    LRM - IML
    DTM - IML
    NBM - IML
    MT - IMT
}

package "Communication Layer" {
    [Network Manager] as NM
    [Privacy Controller] as PC
    [Differential Privacy] as DPr
    [Encryption Handler] as EH

    interface "INetworkManager" as INM
    interface "IPrivacyController" as IPC

    NM - INM
    PC - IPC
}

package "Evaluation Layer" {
    [Performance Evaluator] as PE
    [Metrics Collector] as MC
    [Report Generator] as RG
    [Visualization Engine] as VE

    interface "IEvaluator" as IE
    interface "IMetricsCollector" as IMC

    PE - IE
    MC - IMC
}

' Dependencies
EDM ..> RM : monitors
EDM ..> AD : uses
FS ..> MA : aggregates
FS ..> CS : selects
FS ..> GMM : manages
DP ..> FE : extracts
DP ..> DD : distributes
MT ..> LRM : trains
MT ..> DTM : trains
MT ..> NBM : trains
NM ..> PC : secures
PC ..> DPr : applies
PC ..> EH : encrypts
PE ..> MC : collects
PE ..> RG : generates
PE ..> VE : visualizes

@enduml

@startuml deployment_diagram
!theme plain
title Edge IDS Federated Learning - Deployment Diagram

node "Edge Device 1" {
    artifact "edge_device.py" as ED1
    artifact "local_model.pkl" as LM1
    artifact "device_config.json" as DC1
    database "Local Data" as LD1

    ED1 --> LM1
    ED1 --> DC1
    ED1 --> LD1
}

node "Edge Device 2" {
    artifact "edge_device.py" as ED2
    artifact "local_model.pkl" as LM2
    artifact "device_config.json" as DC2
    database "Local Data" as LD2

    ED2 --> LM2
    ED2 --> DC2
    ED2 --> LD2
}

node "Edge Device N" {
    artifact "edge_device.py" as EDN
    artifact "local_model.pkl" as LMN
    artifact "device_config.json" as DCN
    database "Local Data" as LDN

    EDN --> LMN
    EDN --> DCN
    EDN --> LDN
}

node "Federated Server" {
    artifact "federated_server.py" as FS
    artifact "global_model.pkl" as GM
    artifact "server_config.json" as SC
    database "Aggregation Results" as AR

    FS --> GM
    FS --> SC
    FS --> AR
}

node "Evaluation Server" {
    artifact "evaluation_framework.py" as EF
    artifact "ml_metrics_evaluation.py" as MME
    database "Results Database" as RDB
    folder "Reports" as REP
    folder "Plots" as PLT

    EF --> RDB
    MME --> RDB
    EF --> REP
    MME --> PLT
}

cloud "Network Infrastructure" {
    interface "HTTPS/TLS" as HTTPS
    interface "WebSocket" as WS
    interface "REST API" as REST
}

' Connections
ED1 ..> HTTPS : Model Updates
ED2 ..> HTTPS : Model Updates
EDN ..> HTTPS : Model Updates
FS ..> HTTPS : Global Model
FS ..> WS : Real-time Communication
EF ..> REST : Evaluation API

note right of ED1 : "Resource-constrained\ndevices with local\ndata and models"

note right of FS : "Central coordination\nwithout accessing\nraw data"

note right of EF : "Comprehensive\nevaluation and\nreporting"

@enduml

@startuml state_diagram
!theme plain
title Edge Device State Machine

[*] --> Initializing

Initializing : Entry / Load configuration
Initializing : Entry / Initialize resources
Initializing --> Ready : initialization_complete

Ready : Entry / Wait for commands
Ready --> Training : start_training
Ready --> Detecting : start_detection
Ready --> Updating : receive_global_model

Training : Entry / Load local data
Training : Do / Train local model
Training : Do / Monitor resources
Training --> Ready : training_complete
Training --> Failed : resource_exhausted
Training --> Failed : training_error

Detecting : Entry / Load model
Detecting : Do / Process incoming data
Detecting : Do / Detect anomalies
Detecting --> Ready : detection_complete
Detecting --> Failed : detection_error

Updating : Entry / Receive model weights
Updating : Do / Update local model
Updating --> Ready : update_complete
Updating --> Failed : update_error

Failed : Entry / Log error
Failed : Entry / Cleanup resources
Failed --> Ready : recover
Failed --> [*] : shutdown

Ready --> Maintenance : maintenance_required
Maintenance : Entry / Perform maintenance
Maintenance : Do / Resource cleanup
Maintenance --> Ready : maintenance_complete

Ready --> [*] : shutdown_signal

@enduml

@startuml model_structure_logistic_regression
!theme plain
title Logistic Regression Model Structure

package "Input Layer" {
    rectangle "Feature Vector" as FV {
        rectangle "Feature 1" as F1
        rectangle "Feature 2" as F2
        rectangle "..." as F3
        rectangle "Feature 78" as F78
    }
}

package "Processing Layer" {
    rectangle "Linear Combination" as LC {
        rectangle "w₁×x₁ + w₂×x₂ + ... + w₇₈×x₇₈ + b" as LINEAR
    }

    rectangle "Sigmoid Function" as SF {
        rectangle "σ(z) = 1/(1 + e⁻ᶻ)" as SIGMOID
    }
}

package "Output Layer" {
    rectangle "Probability Output" as PO {
        rectangle "P(Normal)" as PN
        rectangle "P(Attack Type 1)" as PA1
        rectangle "P(Attack Type 2)" as PA2
    }
}

package "Decision Layer" {
    rectangle "Classification" as CL {
        rectangle "argmax(probabilities)" as ARGMAX
    }
}

FV --> LC : Input Features
LC --> SF : Linear Output
SF --> PO : Sigmoid Output
PO --> CL : Probabilities

note right of LINEAR : "Weighted sum of\ninput features"
note right of SIGMOID : "Maps to probability\nrange [0,1]"
note right of ARGMAX : "Final class\nprediction"

@enduml

@startuml model_structure_decision_tree
!theme plain
title Decision Tree Model Structure

package "Root Node" {
    rectangle "Feature X₁ ≤ threshold₁?" as ROOT
}

package "Level 1" {
    rectangle "Feature X₂ ≤ threshold₂?" as L1_LEFT
    rectangle "Feature X₃ ≤ threshold₃?" as L1_RIGHT
}

package "Level 2" {
    rectangle "Feature X₄ ≤ threshold₄?" as L2_LL
    rectangle "Leaf: Normal" as L2_LR
    rectangle "Leaf: Attack1" as L2_RL
    rectangle "Feature X₅ ≤ threshold₅?" as L2_RR
}

package "Level 3" {
    rectangle "Leaf: Attack2" as L3_L
    rectangle "Leaf: Normal" as L3_R
    rectangle "Leaf: Attack1" as L3_RL
    rectangle "Leaf: Attack2" as L3_RR
}

ROOT --> L1_LEFT : Yes
ROOT --> L1_RIGHT : No
L1_LEFT --> L2_LL : Yes
L1_LEFT --> L2_LR : No
L1_RIGHT --> L2_RL : Yes
L1_RIGHT --> L2_RR : No
L2_LL --> L3_L : Yes
L2_LL --> L3_R : No
L2_RR --> L3_RL : Yes
L2_RR --> L3_RR : No

note right of ROOT : "Information gain\nbased splitting"
note left of L2_LR : "Pure leaf nodes\nfor classification"

@enduml

@startuml model_structure_naive_bayes
!theme plain
title Naive Bayes Model Structure

package "Input Features" {
    rectangle "X₁" as X1
    rectangle "X₂" as X2
    rectangle "..." as X3
    rectangle "X₇₈" as X78
}

package "Class Priors" {
    rectangle "P(Normal)" as PC1
    rectangle "P(Attack1)" as PC2
    rectangle "P(Attack2)" as PC3
}

package "Feature Likelihoods" {
    rectangle "P(X₁|Normal)" as PX1C1
    rectangle "P(X₁|Attack1)" as PX1C2
    rectangle "P(X₁|Attack2)" as PX1C3

    rectangle "P(X₂|Normal)" as PX2C1
    rectangle "P(X₂|Attack1)" as PX2C2
    rectangle "P(X₂|Attack2)" as PX2C3

    rectangle "..." as DOTS

    rectangle "P(X₇₈|Normal)" as PX78C1
    rectangle "P(X₇₈|Attack1)" as PX78C2
    rectangle "P(X₇₈|Attack2)" as PX78C3
}

package "Posterior Calculation" {
    rectangle "P(Normal|X) ∝ P(Normal) × ∏P(Xᵢ|Normal)" as POST1
    rectangle "P(Attack1|X) ∝ P(Attack1) × ∏P(Xᵢ|Attack1)" as POST2
    rectangle "P(Attack2|X) ∝ P(Attack2) × ∏P(Xᵢ|Attack2)" as POST3
}

package "Classification" {
    rectangle "argmax P(Class|X)" as CLASSIFY
}

X1 --> PX1C1
X1 --> PX1C2
X1 --> PX1C3
X2 --> PX2C1
X2 --> PX2C2
X2 --> PX2C3
X78 --> PX78C1
X78 --> PX78C2
X78 --> PX78C3

PC1 --> POST1
PC2 --> POST2
PC3 --> POST3

PX1C1 --> POST1
PX2C1 --> POST1
PX78C1 --> POST1

PX1C2 --> POST2
PX2C2 --> POST2
PX78C2 --> POST2

PX1C3 --> POST3
PX2C3 --> POST3
PX78C3 --> POST3

POST1 --> CLASSIFY
POST2 --> CLASSIFY
POST3 --> CLASSIFY

note right of POST1 : "Naive independence\nassumption"
note left of CLASSIFY : "Maximum a posteriori\nclassification"

@enduml

@startuml federated_learning_process
!theme plain
title FedAvg Algorithm Process Flow

start

:Initialize Global Model θ₀;
note right: Random initialization\nor pre-trained weights

:Set Round t = 1;

while (t ≤ T?) is (yes)

    :Select Subset of Clients Sₜ;
    note right: Random selection or\nbased on availability

    :Broadcast θₜ₋₁ to Selected Clients;

    fork
        :Client 1: Local Training;
        :θₜ¹ = LocalUpdate(θₜ₋₁, D₁);
        note right: E epochs on\nlocal dataset D₁
    fork again
        :Client 2: Local Training;
        :θₜ² = LocalUpdate(θₜ₋₁, D₂);
    fork again
        :Client k: Local Training;
        :θₜᵏ = LocalUpdate(θₜ₋₁, Dₖ);
    end fork

    :Collect Model Updates;
    :Δθₜᵏ = θₜᵏ - θₜ₋₁ for all k ∈ Sₜ;

    if (Apply Differential Privacy?) then (yes)
        :Add Noise to Updates;
        :Δθₜᵏ = Δθₜᵏ + N(0, σ²I);
    endif

    :Aggregate Updates (FedAvg);
    :θₜ = θₜ₋₁ + η × Σ(nₖ/n × Δθₜᵏ);
    note right: Weighted average\nbased on local data size

    :Evaluate Global Model;
    :Compute Accuracy, F1-Score;

    :t = t + 1;

endwhile (no)

:Return Final Global Model θₜ;

stop

@enduml

@startuml use_case_diagram
!theme plain
title Edge IDS Federated Learning - Use Case Diagram

left to right direction

actor "Network Administrator" as NA
actor "Security Analyst" as SA
actor "System Administrator" as SysA
actor "Edge Device" as ED
actor "Federated Server" as FS

package "Edge IDS System" {

    usecase "Configure Edge Devices" as UC1
    usecase "Monitor Network Traffic" as UC2
    usecase "Detect Intrusions" as UC3
    usecase "Train Local Models" as UC4
    usecase "Participate in FL" as UC5
    usecase "Aggregate Models" as UC6
    usecase "Distribute Global Model" as UC7
    usecase "Generate Reports" as UC8
    usecase "Analyze Performance" as UC9
    usecase "Manage Resources" as UC10
    usecase "Apply Privacy Protection" as UC11
    usecase "Evaluate System" as UC12

    package "Real-time Operations" {
        usecase "Stream Processing" as UC13
        usecase "Anomaly Detection" as UC14
        usecase "Alert Generation" as UC15
    }

    package "Federated Learning" {
        usecase "Client Selection" as UC16
        usecase "Model Aggregation" as UC17
        usecase "Differential Privacy" as UC18
    }
}

' Relationships
NA --> UC1
NA --> UC8
NA --> UC10

SA --> UC2
SA --> UC3
SA --> UC9
SA --> UC12
SA --> UC15

SysA --> UC1
SysA --> UC10
SysA --> UC8

ED --> UC2
ED --> UC3
ED --> UC4
ED --> UC5
ED --> UC13
ED --> UC14

FS --> UC6
FS --> UC7
FS --> UC16
FS --> UC17
FS --> UC18

' Extensions and Includes
UC3 ..> UC14 : <<include>>
UC5 ..> UC4 : <<include>>
UC6 ..> UC17 : <<include>>
UC7 ..> UC11 : <<include>>
UC14 ..> UC15 : <<extend>>
UC17 ..> UC18 : <<extend>>

@enduml

@startuml communication_diagram
!theme plain
title Federated Learning Communication Flow

object "Edge Device 1" as ED1
object "Edge Device 2" as ED2
object "Edge Device 3" as ED3
object "Federated Server" as FS
object "Evaluation System" as ES

ED1 -> FS : 1: register_device()
ED2 -> FS : 2: register_device()
ED3 -> FS : 3: register_device()

FS -> ED1 : 4: send_global_model()
FS -> ED2 : 5: send_global_model()
FS -> ED3 : 6: send_global_model()

ED1 -> ED1 : 7: train_local_model()
ED2 -> ED2 : 8: train_local_model()
ED3 -> ED3 : 9: train_local_model()

ED1 -> FS : 10: send_model_update()
ED2 -> FS : 11: send_model_update()
ED3 -> FS : 12: send_model_update()

FS -> FS : 13: aggregate_models()
FS -> FS : 14: update_global_model()

FS -> ES : 15: send_round_results()
ES -> ES : 16: evaluate_performance()

note right of FS : "Coordinates federated\nlearning rounds"
note left of ED1 : "Trains on local data\nwithout sharing"

@enduml

@startuml package_diagram
!theme plain
title Edge IDS System - Package Structure

package "edge_ids_simulation" {

    package "core" {
        class EdgeDevice
        class FederatedServer
        class EdgeIDSSimulation
    }

    package "data" {
        class EdgeDataProcessor
        class DataDistributor
        class StreamProcessor
    }

    package "models" {
        class LogisticRegressionModel
        class DecisionTreeModel
        class NaiveBayesModel
        class ModelTrainer
    }

    package "evaluation" {
        class EvaluationFramework
        class PerformanceMetrics
        class ResourceAnalyzer
    }

    package "communication" {
        class NetworkManager
        class PrivacyController
        class DifferentialPrivacy
    }

    package "utils" {
        class ResourceMonitor
        class Logger
        class ConfigManager
        class Visualizer
    }

    package "config" {
        class Settings
        class DeviceProfiles
        class ModelConfigs
    }
}

' Dependencies
core ..> data : uses
core ..> models : uses
core ..> communication : uses
core ..> utils : uses
core ..> config : reads

evaluation ..> core : evaluates
evaluation ..> utils : uses

models ..> utils : uses
communication ..> utils : uses

@enduml

@startuml timing_diagram
!theme plain
title Federated Learning Round Timing

robust "Federated Server" as FS
robust "Edge Device 1" as ED1
robust "Edge Device 2" as ED2
robust "Edge Device 3" as ED3

@0
FS is Idle
ED1 is Idle
ED2 is Idle
ED3 is Idle

@5
FS is Selecting_Clients
ED1 is Waiting
ED2 is Waiting
ED3 is Waiting

@10
FS is Distributing_Model
ED1 is Receiving_Model
ED2 is Receiving_Model
ED3 is Receiving_Model

@15
FS is Waiting_Updates
ED1 is Training
ED2 is Training
ED3 is Training

@45
FS is Waiting_Updates
ED1 is Sending_Update
ED2 is Training
ED3 is Training

@50
FS is Waiting_Updates
ED1 is Idle
ED2 is Sending_Update
ED3 is Training

@55
FS is Waiting_Updates
ED1 is Idle
ED2 is Idle
ED3 is Sending_Update

@60
FS is Aggregating
ED1 is Idle
ED2 is Idle
ED3 is Idle

@70
FS is Evaluating
ED1 is Idle
ED2 is Idle
ED3 is Idle

@75
FS is Idle
ED1 is Idle
ED2 is Idle
ED3 is Idle

@FS
0 is Idle
5 is Selecting_Clients
10 is Distributing_Model
15 is Waiting_Updates
60 is Aggregating
70 is Evaluating
75 is Idle

@enduml

@startuml object_diagram
!theme plain
title Edge IDS System - Object Instance Diagram

object "simulation : EdgeIDSSimulation" as sim {
    is_running = true
    current_round = 15
    total_rounds = 20
}

object "server : FederatedServer" as server {
    global_model = LogisticRegression()
    participating_devices = [0,1,2,3,4,5,6,7]
    aggregation_method = "FedAvg"
}

object "device_0 : EdgeDevice" as dev0 {
    device_id = 0
    device_profile = "low_resource"
    is_healthy = true
    local_accuracy = 0.89
}

object "device_1 : EdgeDevice" as dev1 {
    device_id = 1
    device_profile = "medium_resource"
    is_healthy = true
    local_accuracy = 0.92
}

object "device_2 : EdgeDevice" as dev2 {
    device_id = 2
    device_profile = "high_resource"
    is_healthy = true
    local_accuracy = 0.94
}

object "processor : EdgeDataProcessor" as proc {
    label_encoder = LabelEncoder()
    scaler = StandardScaler()
    distribution_type = "non_iid"
}

object "evaluator : EvaluationFramework" as eval {
    evaluation_results = {}
    comparison_enabled = true
}

sim *-- server
sim *-- dev0
sim *-- dev1
sim *-- dev2
sim *-- proc
sim *-- eval

server --> dev0 : coordinates
server --> dev1 : coordinates
server --> dev2 : coordinates

@enduml
