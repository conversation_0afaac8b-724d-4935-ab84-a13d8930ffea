"""
Evaluation Framework for Edge IDS Federated Learning
Comprehensive evaluation addressing research objectives and comparison with baselines.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import time
import json
import logging
from typing import Dict, List, Tuple, Any
from datetime import datetime

from config import *
from edge_data_processor import EdgeDataProcessor

class EvaluationFramework:
    """
    Comprehensive evaluation framework for the Edge IDS system.
    Addresses all research objectives and provides detailed analysis.
    """
    
    def __init__(self):
        self.data_processor = EdgeDataProcessor()
        self.evaluation_results = {}
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup evaluation logging."""
        logger = logging.getLogger('EvaluationFramework')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(f'{LOGS_DIR}/evaluation.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def evaluate_federated_vs_centralized(self, federated_results: Dict, 
                                        test_data: Tuple[pd.DataFrame, pd.Series]) -> Dict:
        """
        Compare federated learning approach with centralized baselines.
        Addresses research objective 4: evaluation and comparison.
        """
        self.logger.info("Evaluating federated vs centralized approaches")
        
        X_test, y_test = test_data
        comparison_results = {
            'federated': self._extract_federated_metrics(federated_results),
            'centralized': {}
        }
        
        if ENABLE_CENTRALIZED_COMPARISON:
            # Train centralized models for comparison
            X_train, y_train = self._prepare_centralized_data()
            
            for model_name in CENTRALIZED_MODEL_TYPES:
                self.logger.info(f"Training centralized {model_name}")
                centralized_metrics = self._train_centralized_model(
                    model_name, X_train, y_train, X_test, y_test
                )
                comparison_results['centralized'][model_name] = centralized_metrics
        
        # Generate comparison analysis
        comparison_analysis = self._analyze_federated_vs_centralized(comparison_results)
        comparison_results['analysis'] = comparison_analysis
        
        return comparison_results
    
    def _extract_federated_metrics(self, federated_results: Dict) -> Dict:
        """Extract key metrics from federated learning results."""
        if not federated_results.get('global_metrics'):
            return {}
        
        global_metrics = federated_results['global_metrics']
        
        # Extract final and best metrics
        final_metrics = global_metrics[-1] if global_metrics else {}
        
        accuracies = [m.get('accuracy', 0) for m in global_metrics]
        f1_scores = [m.get('f1_score', 0) for m in global_metrics]
        inference_times = [m.get('inference_time', 0) for m in global_metrics]
        
        return {
            'final_accuracy': final_metrics.get('accuracy', 0),
            'final_f1_score': final_metrics.get('f1_score', 0),
            'final_precision': final_metrics.get('precision', 0),
            'final_recall': final_metrics.get('recall', 0),
            'max_accuracy': max(accuracies) if accuracies else 0,
            'avg_accuracy': np.mean(accuracies) if accuracies else 0,
            'max_f1_score': max(f1_scores) if f1_scores else 0,
            'avg_f1_score': np.mean(f1_scores) if f1_scores else 0,
            'avg_inference_time': np.mean(inference_times) if inference_times else 0,
            'total_rounds': len(global_metrics),
            'convergence_round': self._find_convergence_round(accuracies)
        }
    
    def _prepare_centralized_data(self) -> Tuple[pd.DataFrame, pd.Series]:
        """Prepare data for centralized model training."""
        # Load the same dataset used for federated learning
        X, y = self.data_processor.load_and_prepare_edge_dataset()
        
        # Use the same train-test split ratio
        from sklearn.model_selection import train_test_split
        X_train, _, y_train, _ = train_test_split(
            X, y, test_size=1-TRAIN_TEST_SPLIT, random_state=42, stratify=y
        )
        
        return X_train, y_train
    
    def _train_centralized_model(self, model_name: str, X_train: pd.DataFrame, 
                                y_train: pd.Series, X_test: pd.DataFrame, 
                                y_test: pd.Series) -> Dict:
        """Train and evaluate a centralized model."""
        start_time = time.time()
        
        # Initialize model
        if model_name == 'logistic_regression':
            model = LogisticRegression(**LIGHTWEIGHT_MODELS['logistic_regression'])
        elif model_name == 'random_forest':
            model = RandomForestClassifier(n_estimators=50, max_depth=10, random_state=42)
        elif model_name == 'svm':
            model = SVC(kernel='linear', random_state=42)
        else:
            raise ValueError(f"Unknown model type: {model_name}")
        
        # Train model
        training_start = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - training_start
        
        # Evaluate model
        inference_start = time.time()
        y_pred = model.predict(X_test)
        inference_time = time.time() - inference_start
        
        # Calculate metrics
        metrics = {
            'accuracy': accuracy_score(y_test, y_pred),
            'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
            'training_time': training_time,
            'inference_time': inference_time,
            'total_time': time.time() - start_time,
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }
        
        return metrics
    
    def _find_convergence_round(self, accuracies: List[float], threshold: float = 0.01) -> int:
        """Find the round where the model converged (accuracy stabilized)."""
        if len(accuracies) < 3:
            return len(accuracies)
        
        for i in range(2, len(accuracies)):
            # Check if accuracy change is below threshold for last 3 rounds
            recent_changes = [abs(accuracies[j] - accuracies[j-1]) for j in range(i-2, i+1)]
            if all(change < threshold for change in recent_changes):
                return i + 1
        
        return len(accuracies)
    
    def _analyze_federated_vs_centralized(self, comparison_results: Dict) -> Dict:
        """Analyze the comparison between federated and centralized approaches."""
        federated = comparison_results['federated']
        centralized = comparison_results['centralized']
        
        analysis = {
            'accuracy_comparison': {},
            'efficiency_comparison': {},
            'privacy_advantages': {},
            'resource_advantages': {}
        }
        
        # Accuracy comparison
        fed_accuracy = federated.get('final_accuracy', 0)
        for model_name, metrics in centralized.items():
            cent_accuracy = metrics.get('accuracy', 0)
            analysis['accuracy_comparison'][model_name] = {
                'federated_accuracy': fed_accuracy,
                'centralized_accuracy': cent_accuracy,
                'accuracy_difference': fed_accuracy - cent_accuracy,
                'relative_performance': fed_accuracy / cent_accuracy if cent_accuracy > 0 else 0
            }
        
        # Efficiency comparison
        fed_inference_time = federated.get('avg_inference_time', 0)
        for model_name, metrics in centralized.items():
            cent_inference_time = metrics.get('inference_time', 0)
            analysis['efficiency_comparison'][model_name] = {
                'federated_inference_time': fed_inference_time,
                'centralized_inference_time': cent_inference_time,
                'inference_speedup': cent_inference_time / fed_inference_time if fed_inference_time > 0 else 0
            }
        
        # Privacy advantages (qualitative assessment)
        analysis['privacy_advantages'] = {
            'data_locality': 'Federated learning keeps data local on edge devices',
            'no_raw_data_sharing': 'Only model updates are shared, not raw data',
            'differential_privacy': 'Can apply differential privacy to model updates',
            'reduced_attack_surface': 'No central data repository vulnerable to attacks'
        }
        
        # Resource advantages
        analysis['resource_advantages'] = {
            'distributed_computation': 'Training load distributed across edge devices',
            'reduced_bandwidth': 'Only model updates transmitted, not raw data',
            'scalability': 'Can scale to many edge devices without central bottleneck',
            'fault_tolerance': 'System continues to work even if some devices fail'
        }
        
        return analysis
    
    def evaluate_resource_efficiency(self, simulation_results: Dict) -> Dict:
        """
        Evaluate resource efficiency of the edge IDS system.
        Addresses research objective 4: resource usage evaluation.
        """
        self.logger.info("Evaluating resource efficiency")
        
        device_metrics = simulation_results.get('device_metrics', [])
        if not device_metrics:
            return {}
        
        # Aggregate resource metrics across all devices and rounds
        resource_analysis = {
            'training_time': [],
            'inference_time': [],
            'memory_usage': [],
            'model_size': [],
            'samples_processed': []
        }
        
        for round_data in device_metrics:
            for device_data in round_data.get('devices', []):
                metrics = device_data.get('metrics', {})
                for metric_name in resource_analysis.keys():
                    if metric_name in metrics:
                        resource_analysis[metric_name].append(metrics[metric_name])
        
        # Calculate statistics
        resource_stats = {}
        for metric_name, values in resource_analysis.items():
            if values:
                resource_stats[metric_name] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'median': np.median(values)
                }
        
        # Resource efficiency analysis
        efficiency_analysis = self._analyze_resource_efficiency(resource_stats)
        
        return {
            'resource_statistics': resource_stats,
            'efficiency_analysis': efficiency_analysis
        }
    
    def _analyze_resource_efficiency(self, resource_stats: Dict) -> Dict:
        """Analyze resource efficiency characteristics."""
        analysis = {}
        
        # Training efficiency
        if 'training_time' in resource_stats:
            training_stats = resource_stats['training_time']
            analysis['training_efficiency'] = {
                'avg_training_time': training_stats['mean'],
                'training_consistency': 1 / (training_stats['std'] + 1e-6),  # Lower std = higher consistency
                'lightweight_assessment': 'Lightweight' if training_stats['mean'] < 60 else 'Heavy'
            }
        
        # Inference efficiency
        if 'inference_time' in resource_stats:
            inference_stats = resource_stats['inference_time']
            analysis['inference_efficiency'] = {
                'avg_inference_time': inference_stats['mean'],
                'real_time_capability': inference_stats['mean'] < 1.0,  # Can process in real-time
                'latency_assessment': 'Low' if inference_stats['mean'] < 0.1 else 'High'
            }
        
        # Memory efficiency
        if 'memory_usage' in resource_stats:
            memory_stats = resource_stats['memory_usage']
            analysis['memory_efficiency'] = {
                'avg_memory_usage_mb': memory_stats['mean'],
                'memory_footprint': 'Small' if memory_stats['mean'] < 100 else 'Large',
                'edge_suitable': memory_stats['mean'] < 512  # Suitable for edge devices
            }
        
        # Model size efficiency
        if 'model_size' in resource_stats:
            model_stats = resource_stats['model_size']
            analysis['model_efficiency'] = {
                'avg_model_size_mb': model_stats['mean'],
                'model_compactness': 'Compact' if model_stats['mean'] < 10 else 'Large',
                'deployment_feasibility': model_stats['mean'] < 20  # Feasible for edge deployment
            }
        
        return analysis
    
    def evaluate_adaptability(self, simulation_results: Dict) -> Dict:
        """
        Evaluate the adaptability of the system to new attack patterns.
        Addresses research objective 3: adaptive real-time threat detection.
        """
        self.logger.info("Evaluating system adaptability")
        
        global_metrics = simulation_results.get('global_metrics', [])
        if len(global_metrics) < 3:
            return {'error': 'Insufficient data for adaptability analysis'}
        
        # Analyze accuracy trends
        accuracies = [m.get('accuracy', 0) for m in global_metrics]
        f1_scores = [m.get('f1_score', 0) for m in global_metrics]
        
        adaptability_analysis = {
            'learning_curve': self._analyze_learning_curve(accuracies),
            'performance_stability': self._analyze_performance_stability(accuracies, f1_scores),
            'convergence_analysis': self._analyze_convergence(accuracies),
            'adaptation_speed': self._analyze_adaptation_speed(accuracies)
        }
        
        return adaptability_analysis
    
    def _analyze_learning_curve(self, accuracies: List[float]) -> Dict:
        """Analyze the learning curve characteristics."""
        if len(accuracies) < 2:
            return {}
        
        # Calculate improvement rate
        improvements = [accuracies[i] - accuracies[i-1] for i in range(1, len(accuracies))]
        
        return {
            'initial_accuracy': accuracies[0],
            'final_accuracy': accuracies[-1],
            'total_improvement': accuracies[-1] - accuracies[0],
            'avg_improvement_per_round': np.mean(improvements),
            'learning_trend': 'Improving' if accuracies[-1] > accuracies[0] else 'Declining',
            'learning_rate': (accuracies[-1] - accuracies[0]) / len(accuracies)
        }
    
    def _analyze_performance_stability(self, accuracies: List[float], f1_scores: List[float]) -> Dict:
        """Analyze performance stability over time."""
        return {
            'accuracy_variance': np.var(accuracies),
            'accuracy_stability': 1 / (np.var(accuracies) + 1e-6),
            'f1_variance': np.var(f1_scores),
            'f1_stability': 1 / (np.var(f1_scores) + 1e-6),
            'overall_stability': 'Stable' if np.var(accuracies) < 0.01 else 'Unstable'
        }
    
    def _analyze_convergence(self, accuracies: List[float]) -> Dict:
        """Analyze convergence characteristics."""
        convergence_round = self._find_convergence_round(accuracies)
        
        return {
            'convergence_round': convergence_round,
            'converged': convergence_round < len(accuracies),
            'convergence_speed': 'Fast' if convergence_round <= len(accuracies) * 0.5 else 'Slow',
            'final_stability': np.std(accuracies[-3:]) if len(accuracies) >= 3 else 0
        }
    
    def _analyze_adaptation_speed(self, accuracies: List[float]) -> Dict:
        """Analyze how quickly the system adapts to changes."""
        if len(accuracies) < 5:
            return {}
        
        # Look for rapid improvements (adaptation events)
        rapid_improvements = []
        for i in range(1, len(accuracies)):
            improvement = accuracies[i] - accuracies[i-1]
            if improvement > 0.05:  # 5% improvement threshold
                rapid_improvements.append(i)
        
        return {
            'rapid_adaptation_events': len(rapid_improvements),
            'adaptation_rounds': rapid_improvements,
            'avg_adaptation_magnitude': np.mean([accuracies[i] - accuracies[i-1] 
                                               for i in rapid_improvements]) if rapid_improvements else 0,
            'adaptation_capability': 'High' if len(rapid_improvements) > 0 else 'Low'
        }
    
    def generate_comprehensive_report(self, simulation_results: Dict, 
                                    comparison_results: Dict = None) -> Dict:
        """Generate a comprehensive evaluation report."""
        self.logger.info("Generating comprehensive evaluation report")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'simulation_summary': simulation_results.get('summary', {}),
            'resource_efficiency': self.evaluate_resource_efficiency(simulation_results),
            'adaptability': self.evaluate_adaptability(simulation_results),
            'research_objectives_assessment': self._assess_research_objectives(simulation_results)
        }
        
        if comparison_results:
            report['federated_vs_centralized'] = comparison_results
        
        # Generate recommendations
        report['recommendations'] = self._generate_recommendations(report)
        
        return report
    
    def _assess_research_objectives(self, simulation_results: Dict) -> Dict:
        """Assess how well the system addresses the research objectives."""
        summary = simulation_results.get('summary', {})
        
        return {
            'objective_1_lightweight_design': {
                'achieved': summary.get('avg_communication_cost', 0) < 1.0,
                'evidence': 'Low communication costs and resource-constrained device simulation',
                'score': 'High' if summary.get('avg_communication_cost', 0) < 1.0 else 'Medium'
            },
            'objective_2_federated_architecture': {
                'achieved': summary.get('total_rounds', 0) > 0,
                'evidence': 'Successful federated learning rounds with privacy preservation',
                'score': 'High' if summary.get('total_rounds', 0) >= 10 else 'Medium'
            },
            'objective_3_real_time_detection': {
                'achieved': summary.get('final_accuracy', 0) > 0.8,
                'evidence': 'High detection accuracy with real-time processing capability',
                'score': 'High' if summary.get('final_accuracy', 0) > 0.9 else 'Medium'
            },
            'objective_4_comprehensive_evaluation': {
                'achieved': True,
                'evidence': 'Multi-dimensional evaluation including accuracy, efficiency, and adaptability',
                'score': 'High'
            }
        }
    
    def _generate_recommendations(self, report: Dict) -> List[str]:
        """Generate recommendations based on evaluation results."""
        recommendations = []
        
        # Performance recommendations
        final_accuracy = report.get('simulation_summary', {}).get('final_accuracy', 0)
        if final_accuracy < 0.9:
            recommendations.append("Consider increasing the number of federated learning rounds to improve accuracy")
        
        # Resource efficiency recommendations
        resource_efficiency = report.get('resource_efficiency', {})
        if resource_efficiency:
            training_stats = resource_efficiency.get('resource_statistics', {}).get('training_time', {})
            if training_stats.get('mean', 0) > 60:
                recommendations.append("Optimize model complexity to reduce training time on edge devices")
        
        # Adaptability recommendations
        adaptability = report.get('adaptability', {})
        if adaptability.get('adaptation_capability') == 'Low':
            recommendations.append("Implement more sophisticated adaptation mechanisms for evolving threats")
        
        return recommendations
    
    def save_evaluation_report(self, report: Dict, filename: str = None):
        """Save the evaluation report to file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{RESULTS_DIR}/evaluation_report_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Evaluation report saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Failed to save evaluation report: {str(e)}")
