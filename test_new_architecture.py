"""
Test script for the new Edge IDS Federated Learning architecture.
This script verifies that all components work correctly together.
"""

import sys
import traceback
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_imports():
    """Test that all modules can be imported successfully."""
    print("🔍 Testing imports...")
    
    try:
        import config
        print("✅ config.py imported successfully")
        
        from edge_device import EdgeDevice
        print("✅ EdgeDevice imported successfully")
        
        from federated_server import FederatedServer
        print("✅ FederatedServer imported successfully")
        
        from edge_data_processor import EdgeDataProcessor
        print("✅ EdgeDataProcessor imported successfully")
        
        from edge_ids_simulation import EdgeIDSSimulation
        print("✅ EdgeIDSSimulation imported successfully")
        
        from evaluation_framework import EvaluationFramework
        print("✅ EvaluationFramework imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        traceback.print_exc()
        return False

def test_data_processor():
    """Test the data processor functionality."""
    print("\n🔍 Testing EdgeDataProcessor...")
    
    try:
        from edge_data_processor import EdgeDataProcessor
        import pandas as pd
        import numpy as np
        
        processor = EdgeDataProcessor()
        
        # Create dummy data for testing
        np.random.seed(42)
        n_samples = 1000
        n_features = 10
        
        X_dummy = pd.DataFrame(
            np.random.randn(n_samples, n_features),
            columns=[f'feature_{i}' for i in range(n_features)]
        )
        y_dummy = pd.Series(np.random.randint(0, 3, n_samples))
        
        # Test federated data splits
        device_data = processor.create_federated_data_splits(X_dummy, y_dummy, 3)
        
        print(f"✅ Created data splits for {len(device_data)-1} devices")
        print(f"✅ Global test set shape: {device_data['global_test']['X_test'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ EdgeDataProcessor test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_edge_device():
    """Test edge device functionality."""
    print("\n🔍 Testing EdgeDevice...")
    
    try:
        from edge_device import EdgeDevice
        from config import EDGE_DEVICE_PROFILES
        import pandas as pd
        import numpy as np
        
        # Create test device
        profile = EDGE_DEVICE_PROFILES['low_resource']
        device = EdgeDevice(device_id=0, device_profile=profile)
        
        # Create dummy training data
        np.random.seed(42)
        X_train = pd.DataFrame(
            np.random.randn(100, 10),
            columns=[f'feature_{i}' for i in range(10)]
        )
        y_train = pd.Series(np.random.randint(0, 3, 100))
        
        # Load data and train
        device.load_local_data(X_train, y_train)
        success = device.train_local_model()
        
        if success:
            print("✅ Edge device training successful")
            
            # Test model weights extraction
            weights = device.get_model_weights()
            if weights:
                print("✅ Model weights extraction successful")
            
            # Test real-time detection
            X_test = pd.DataFrame(
                np.random.randn(10, 10),
                columns=[f'feature_{i}' for i in range(10)]
            )
            detection_result = device.real_time_detection(X_test)
            if detection_result:
                print("✅ Real-time detection successful")
        
        device.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ EdgeDevice test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_federated_server():
    """Test federated server functionality."""
    print("\n🔍 Testing FederatedServer...")
    
    try:
        from federated_server import FederatedServer
        from edge_device import EdgeDevice
        from config import EDGE_DEVICE_PROFILES
        import pandas as pd
        import numpy as np
        
        # Create server
        server = FederatedServer()
        
        # Create test devices
        devices = []
        for i in range(3):
            profile = EDGE_DEVICE_PROFILES['low_resource']
            device = EdgeDevice(device_id=i, device_profile=profile)
            
            # Add dummy data
            X_train = pd.DataFrame(
                np.random.randn(50, 10),
                columns=[f'feature_{i}' for i in range(10)]
            )
            y_train = pd.Series(np.random.randint(0, 3, 50))
            device.load_local_data(X_train, y_train)
            
            devices.append(device)
        
        # Test device selection
        selected = server.select_participating_devices(devices)
        print(f"✅ Selected {len(selected)} devices for participation")
        
        # Test coordination (simplified)
        if selected:
            print("✅ Federated server basic functionality working")
        
        # Cleanup
        for device in devices:
            device.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ FederatedServer test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration settings."""
    print("\n🔍 Testing configuration...")
    
    try:
        import config
        
        # Check key configuration parameters
        assert hasattr(config, 'NUM_EDGE_DEVICES'), "NUM_EDGE_DEVICES not defined"
        assert hasattr(config, 'FL_ROUNDS'), "FL_ROUNDS not defined"
        assert hasattr(config, 'EDGE_DEVICE_PROFILES'), "EDGE_DEVICE_PROFILES not defined"
        assert hasattr(config, 'MAX_TOTAL_SAMPLES'), "MAX_TOTAL_SAMPLES not defined"
        
        print(f"✅ Configuration loaded successfully")
        print(f"   - Edge devices: {config.NUM_EDGE_DEVICES}")
        print(f"   - FL rounds: {config.FL_ROUNDS}")
        print(f"   - Max samples: {config.MAX_TOTAL_SAMPLES}")
        print(f"   - Device profiles: {list(config.EDGE_DEVICE_PROFILES.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_directories():
    """Test that required directories exist."""
    print("\n🔍 Testing directory structure...")
    
    try:
        import os
        from config import RESULTS_DIR, LOGS_DIR, MODELS_DIR, PLOTS_DIR
        
        directories = [RESULTS_DIR, LOGS_DIR, MODELS_DIR, PLOTS_DIR]
        
        for directory in directories:
            if os.path.exists(directory):
                print(f"✅ Directory exists: {directory}")
            else:
                print(f"⚠️  Directory missing (will be created): {directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ Directory test failed: {str(e)}")
        return False

def run_all_tests():
    """Run all tests and provide summary."""
    print("🚀 Starting Edge IDS Architecture Tests")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Directories", test_directories),
        ("Imports", test_imports),
        ("Data Processor", test_data_processor),
        ("Edge Device", test_edge_device),
        ("Federated Server", test_federated_server),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The new architecture is ready to use.")
        print("\nNext steps:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Add your dataset to data/raw_dataset.csv")
        print("3. Run the simulation: python edge_ids_simulation.py")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please fix the issues before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
