"""
Edge Data Processing Module
Lightweight data processing specifically designed for edge computing constraints.
Focuses on real-time processing, minimal memory usage, and privacy preservation.
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import time
import logging
from typing import Tuple, Dict, List, Generator
import random
from config import *

class EdgeDataProcessor:
    """
    Lightweight data processor for edge devices with resource constraints.
    Implements streaming data processing and privacy-preserving techniques.
    """
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.feature_names = None
        self.is_fitted = False
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Data statistics for monitoring
        self.data_stats = {
            'total_samples_processed': 0,
            'normal_samples': 0,
            'attack_samples': 0,
            'processing_time': 0
        }
    
    def _setup_logging(self):
        """Setup logging for data processor."""
        logger = logging.getLogger('EdgeDataProcessor')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(f'{LOGS_DIR}/edge_data_processor.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def load_and_prepare_edge_dataset(self) -> Tuple[pd.DataFrame, pd.Series]:
        """
        Load and prepare dataset for edge computing simulation.
        Uses only a subset of data to simulate edge constraints.
        """
        self.logger.info("Loading dataset for edge simulation...")
        start_time = time.time()
        
        try:
            # Load raw dataset
            df = pd.read_csv(DATASET_PATH, low_memory=False)
            self.logger.info(f"Original dataset shape: {df.shape}")
            
            # Use only a subset for edge simulation
            if len(df) > MAX_TOTAL_SAMPLES:
                df = df.sample(n=MAX_TOTAL_SAMPLES, random_state=42)
                self.logger.info(f"Sampled dataset to {MAX_TOTAL_SAMPLES} samples for edge simulation")
            
            # Clean column names
            df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_').str.replace('/', '_')
            
            # Identify label column
            label_column = self._identify_label_column(df)
            if label_column is None:
                raise ValueError("Could not identify label column")
            
            # Separate features and labels
            X = df.drop(columns=[label_column])
            y = df[label_column]
            
            # Basic preprocessing
            X, y = self._basic_preprocessing(X, y)
            
            # Store feature names
            self.feature_names = X.columns.tolist()
            
            processing_time = time.time() - start_time
            self.logger.info(f"Dataset prepared in {processing_time:.2f}s, final shape: {X.shape}")
            
            return X, y
            
        except Exception as e:
            self.logger.error(f"Failed to load dataset: {str(e)}")
            raise
    
    def _identify_label_column(self, df: pd.DataFrame) -> str:
        """Identify the label column in the dataset."""
        label_candidates = ['label', 'attack', 'class', 'target']
        
        for col in label_candidates:
            if col in df.columns:
                return col
        
        # If not found, assume last column is label
        return df.columns[-1]
    
    def _basic_preprocessing(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """
        Perform basic preprocessing suitable for edge devices.
        Focus on lightweight operations.
        """
        # Handle missing values in labels
        if y.isnull().any():
            y = y.fillna(y.mode()[0])
        
        # Convert features to numeric
        for col in X.columns:
            X[col] = pd.to_numeric(X[col], errors='coerce')
        
        # Handle infinite values
        X.replace([np.inf, -np.inf], np.nan, inplace=True)
        
        # Simple feature selection - remove columns with too many missing values
        missing_threshold = 0.5
        X = X.loc[:, X.isnull().mean() < missing_threshold]
        
        # Fill remaining missing values with median (lightweight)
        X = X.fillna(X.median())
        
        # Encode labels
        y_encoded = self.label_encoder.fit_transform(y)
        
        self.logger.info(f"Preprocessing completed. Features: {X.shape[1]}, Classes: {len(self.label_encoder.classes_)}")
        
        return X, pd.Series(y_encoded, index=y.index)
    
    def create_federated_data_splits(self, X: pd.DataFrame, y: pd.Series, 
                                   num_devices: int) -> Dict[int, Dict]:
        """
        Create data splits for federated learning simulation.
        Implements different data distribution strategies.
        """
        self.logger.info(f"Creating federated data splits for {num_devices} devices")
        
        # Global train-test split
        X_train_global, X_test_global, y_train_global, y_test_global = train_test_split(
            X, y, test_size=1-TRAIN_TEST_SPLIT, random_state=42, stratify=y
        )
        
        # Create device-specific data splits
        device_data = {}
        
        if DATA_DISTRIBUTION == 'iid':
            device_data = self._create_iid_splits(X_train_global, y_train_global, num_devices)
        elif DATA_DISTRIBUTION == 'non_iid':
            device_data = self._create_non_iid_splits(X_train_global, y_train_global, num_devices)
        elif DATA_DISTRIBUTION == 'unbalanced':
            device_data = self._create_unbalanced_splits(X_train_global, y_train_global, num_devices)
        
        # Add global test set
        device_data['global_test'] = {
            'X_test': X_test_global,
            'y_test': y_test_global
        }
        
        self.logger.info("Federated data splits created successfully")
        return device_data
    
    def _create_iid_splits(self, X: pd.DataFrame, y: pd.Series, num_devices: int) -> Dict:
        """Create IID (Independent and Identically Distributed) data splits."""
        device_data = {}
        samples_per_device = len(X) // num_devices
        
        # Shuffle data
        indices = np.random.permutation(len(X))
        
        for device_id in range(num_devices):
            start_idx = device_id * samples_per_device
            end_idx = start_idx + samples_per_device if device_id < num_devices - 1 else len(X)
            
            device_indices = indices[start_idx:end_idx]
            X_device = X.iloc[device_indices]
            y_device = y.iloc[device_indices]
            
            # Local train-test split
            X_train, X_test, y_train, y_test = train_test_split(
                X_device, y_device, test_size=0.2, random_state=42
            )
            
            device_data[device_id] = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test
            }
        
        return device_data
    
    def _create_non_iid_splits(self, X: pd.DataFrame, y: pd.Series, num_devices: int) -> Dict:
        """Create Non-IID data splits (each device has different class distributions)."""
        device_data = {}
        unique_classes = y.unique()
        
        # Assign dominant classes to each device
        classes_per_device = max(1, len(unique_classes) // num_devices)
        
        for device_id in range(num_devices):
            # Select dominant classes for this device
            start_class = (device_id * classes_per_device) % len(unique_classes)
            device_classes = unique_classes[start_class:start_class + classes_per_device]
            
            # Get samples for dominant classes (80%) and random samples (20%)
            dominant_mask = y.isin(device_classes)
            dominant_samples = X[dominant_mask], y[dominant_mask]
            
            other_samples = X[~dominant_mask], y[~dominant_mask]
            if len(other_samples[0]) > 0:
                random_indices = np.random.choice(
                    len(other_samples[0]), 
                    size=min(len(dominant_samples[0]) // 4, len(other_samples[0])), 
                    replace=False
                )
                random_samples = other_samples[0].iloc[random_indices], other_samples[1].iloc[random_indices]
            else:
                random_samples = pd.DataFrame(), pd.Series()
            
            # Combine samples
            if len(random_samples[0]) > 0:
                X_device = pd.concat([dominant_samples[0], random_samples[0]])
                y_device = pd.concat([dominant_samples[1], random_samples[1]])
            else:
                X_device, y_device = dominant_samples
            
            # Local train-test split
            if len(X_device) > 1:
                X_train, X_test, y_train, y_test = train_test_split(
                    X_device, y_device, test_size=0.2, random_state=42
                )
            else:
                X_train, X_test, y_train, y_test = X_device, pd.DataFrame(), y_device, pd.Series()
            
            device_data[device_id] = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test
            }
        
        return device_data
    
    def _create_unbalanced_splits(self, X: pd.DataFrame, y: pd.Series, num_devices: int) -> Dict:
        """Create unbalanced data splits (devices have different amounts of data)."""
        device_data = {}
        
        # Create random split sizes (some devices get more data)
        split_ratios = np.random.dirichlet(np.ones(num_devices))
        
        indices = np.random.permutation(len(X))
        start_idx = 0
        
        for device_id in range(num_devices):
            samples_for_device = int(len(X) * split_ratios[device_id])
            end_idx = start_idx + samples_for_device
            
            device_indices = indices[start_idx:end_idx]
            X_device = X.iloc[device_indices]
            y_device = y.iloc[device_indices]
            
            # Local train-test split
            if len(X_device) > 1:
                X_train, X_test, y_train, y_test = train_test_split(
                    X_device, y_device, test_size=0.2, random_state=42
                )
            else:
                X_train, X_test, y_train, y_test = X_device, pd.DataFrame(), y_device, pd.Series()
            
            device_data[device_id] = {
                'X_train': X_train,
                'X_test': X_test,
                'y_train': y_train,
                'y_test': y_test
            }
            
            start_idx = end_idx
        
        return device_data
    
    def create_real_time_stream(self, X: pd.DataFrame, y: pd.Series) -> Generator:
        """
        Create a real-time data stream for simulation.
        Yields batches of data with simulated timing.
        """
        self.logger.info("Starting real-time data stream simulation")
        
        # Shuffle data for realistic stream
        indices = np.random.permutation(len(X))
        
        for i in range(0, len(indices), STREAM_BATCH_SIZE):
            batch_indices = indices[i:i + STREAM_BATCH_SIZE]
            X_batch = X.iloc[batch_indices]
            y_batch = y.iloc[batch_indices]
            
            # Inject attacks if enabled
            if ATTACK_INJECTION_RATE > 0:
                X_batch, y_batch = self._inject_attacks(X_batch, y_batch)
            
            yield X_batch, y_batch
            
            # Simulate real-time delay
            if REAL_TIME_SIMULATION:
                time.sleep(STREAM_INTERVAL)
    
    def _inject_attacks(self, X_batch: pd.DataFrame, y_batch: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """Inject simulated attacks into data stream."""
        attack_classes = [i for i, cls in enumerate(self.label_encoder.classes_) 
                         if any(attack in cls.lower() for attack in ['ddos', 'dos', 'attack', 'bot', 'scan'])]
        
        if not attack_classes:
            return X_batch, y_batch
        
        # Randomly inject attacks
        num_attacks = int(len(X_batch) * ATTACK_INJECTION_RATE)
        if num_attacks > 0:
            attack_indices = np.random.choice(len(X_batch), num_attacks, replace=False)
            attack_class = np.random.choice(attack_classes)
            
            y_batch.iloc[attack_indices] = attack_class
        
        return X_batch, y_batch
    
    def get_data_statistics(self) -> Dict:
        """Get data processing statistics."""
        return self.data_stats.copy()
