import numpy as np
from sklearn.linear_model import LogisticRegression

class ModelAggregator:
    """
    A class to simulate the central server that aggregates models from edge clients.
    It performs Federated Averaging (FedAvg) on Logistic Regression models.
    """
    def __init__(self):
        # The global model is initialized. We will update its weights (coefficients and intercept)
        # after each aggregation round.
        self.global_model = LogisticRegression(max_iter=1000, solver='liblinear', random_state=42)
        self.is_first_aggregation = True # Flag to handle the first aggregation carefully

    def initialize_global_model_params(self, initial_coeffs, initial_intercept):
        """
        Initializes the global model's parameters (coefficients and intercept)
        with parameters from the first client's model. This ensures the global
        model has the correct shape for aggregation.
        """
        if self.is_first_aggregation:
            print("Initializing global model parameters with first client's model shape.")
            self.global_model.coef_ = initial_coeffs
            self.global_model.intercept_ = initial_intercept
            self.is_first_aggregation = False

    def aggregate_models(self, client_models_info):
        """
        Aggregates models received from clients using Federated Averaging.
        
        Args:
            client_models_info (list): A list of dictionaries, where each dictionary
                                       contains 'model' (trained client model) and
                                       'data_size' (number of samples client trained on).
                                       Example: [{'model': client_model_1, 'data_size': 100}, ...]

        Returns:
            sklearn.linear_model.LogisticRegression: The newly aggregated global model.
        """
        if not client_models_info:
            print("No client models received for aggregation.")
            return self.global_model

        total_data_size = sum(info['data_size'] for info in client_models_info)
        
        # Initialize aggregated coefficients and intercept
        # For Logistic Regression, these are model.coef_ and model.intercept_
        
        # Ensure that the global model has been initialized before attempting to aggregate
        # This typically happens after the first client trains and sends its model.
        if self.is_first_aggregation:
            print("Warning: Global model not yet initialized. Please ensure at least one client trains before first aggregation.")
            # As a fallback, use the first client's model to initialize
            self.initialize_global_model_params(
                client_models_info[0]['model'].coef_,
                client_models_info[0]['model'].intercept_
            )
        
        # Create empty arrays of the correct shape for aggregation
        aggregated_coef = np.zeros_like(self.global_model.coef_)
        aggregated_intercept = np.zeros_like(self.global_model.intercept_)

        print(f"\n--- Aggregating Models from {len(client_models_info)} Clients ---")
        for i, client_info in enumerate(client_models_info):
            client_model = client_info['model']
            client_data_size = client_info['data_size']

            # Check shape compatibility before aggregation
            if client_model.coef_.shape != self.global_model.coef_.shape:
                print(f"Warning: Client {i+1} model coefficient shape {client_model.coef_.shape} doesn't match global model shape {self.global_model.coef_.shape}. Skipping this client.")
                continue

            if client_model.intercept_.shape != self.global_model.intercept_.shape:
                print(f"Warning: Client {i+1} model intercept shape {client_model.intercept_.shape} doesn't match global model shape {self.global_model.intercept_.shape}. Skipping this client.")
                continue

            # Weighted average of coefficients and intercepts
            weight = client_data_size / total_data_size
            aggregated_coef += weight * client_model.coef_
            aggregated_intercept += weight * client_model.intercept_
            print(f"  Client {i+1}: weight={weight:.4f}, coef_shape={client_model.coef_.shape}, intercept_shape={client_model.intercept_.shape}")

        # Update the global model with the aggregated parameters
        self.global_model.coef_ = aggregated_coef
        self.global_model.intercept_ = aggregated_intercept
        
        print("Aggregation complete. Global model updated.")
        return self.global_model

    def get_global_model(self):
        """
        Returns the current global model.
        """
        return self.global_model

