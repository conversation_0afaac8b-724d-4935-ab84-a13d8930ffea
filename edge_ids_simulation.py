"""
Edge IDS Federated Learning Simulation
Main simulation orchestrator for the lightweight federated learning-based IDS.
"""

import time
import logging
import numpy as np
import pandas as pd
from datetime import datetime
import json
import matplotlib.pyplot as plt
from typing import List, Dict, Any
import threading
import queue

from config import *
from edge_device import EdgeDevice
from federated_server import FederatedServer
from edge_data_processor import EdgeDataProcessor

class EdgeIDSSimulation:
    """
    Main simulation class that orchestrates the federated learning-based IDS
    for edge computing environments.
    """
    
    def __init__(self):
        self.data_processor = EdgeDataProcessor()
        self.federated_server = FederatedServer()
        self.edge_devices = []
        
        # Simulation state
        self.is_running = False
        self.current_round = 0
        
        # Results storage
        self.simulation_results = {
            'rounds': [],
            'device_metrics': [],
            'global_metrics': [],
            'communication_costs': [],
            'resource_usage': []
        }
        
        # Real-time monitoring
        self.real_time_queue = queue.Queue()
        self.monitoring_thread = None
        
        # Setup logging
        self.logger = self._setup_logging()
        
    def _setup_logging(self):
        """Setup simulation logging."""
        logger = logging.getLogger('EdgeIDSSimulation')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        if not logger.handlers:
            handler = logging.FileHandler(f'{LOGS_DIR}/simulation.log')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def initialize_simulation(self):
        """Initialize the simulation environment."""
        self.logger.info("Initializing Edge IDS Federated Learning Simulation")
        
        # 1. Load and prepare data
        self.logger.info("Loading and preparing dataset...")
        X, y = self.data_processor.load_and_prepare_edge_dataset()
        
        # 2. Create federated data splits
        self.logger.info("Creating federated data splits...")
        device_data = self.data_processor.create_federated_data_splits(X, y, NUM_EDGE_DEVICES)
        
        # 3. Initialize edge devices
        self.logger.info("Initializing edge devices...")
        self._initialize_edge_devices(device_data)
        
        # 4. Store global test data
        self.global_test_data = device_data['global_test']
        
        self.logger.info(f"Simulation initialized with {len(self.edge_devices)} edge devices")
        
    def _initialize_edge_devices(self, device_data: Dict):
        """Initialize edge devices with different resource profiles."""
        device_id = 0
        
        for profile_name, profile_config in EDGE_DEVICE_PROFILES.items():
            for _ in range(profile_config['count']):
                # Create edge device
                device = EdgeDevice(
                    device_id=device_id,
                    device_profile=profile_config,
                    model_type=DEFAULT_MODEL
                )
                
                # Load device-specific data
                if device_id in device_data:
                    data = device_data[device_id]
                    device.load_local_data(data['X_train'], data['y_train'])
                
                self.edge_devices.append(device)
                device_id += 1
                
                self.logger.info(f"Initialized device {device_id-1} with profile: {profile_name}")
    
    def run_federated_learning(self):
        """Run the main federated learning simulation."""
        self.logger.info("Starting federated learning simulation")
        self.is_running = True
        
        # Start real-time monitoring if enabled
        if ENABLE_REAL_TIME_PLOTS:
            self._start_real_time_monitoring()
        
        try:
            for round_num in range(1, FL_ROUNDS + 1):
                self.current_round = round_num
                self.logger.info(f"\n{'='*50}")
                self.logger.info(f"FEDERATED LEARNING ROUND {round_num}/{FL_ROUNDS}")
                self.logger.info(f"{'='*50}")
                
                # Execute federated learning round
                round_results = self.federated_server.coordinate_training_round(self.edge_devices)
                
                if round_results:
                    # Evaluate global model
                    global_metrics = self._evaluate_global_model()
                    
                    # Collect device metrics
                    device_metrics = self._collect_device_metrics()
                    
                    # Store results
                    self._store_round_results(round_num, round_results, global_metrics, device_metrics)
                    
                    # Update real-time monitoring
                    if ENABLE_REAL_TIME_PLOTS:
                        self._update_real_time_plots(round_num, global_metrics)
                    
                    # Print round summary
                    self._print_round_summary(round_num, round_results, global_metrics)
                else:
                    self.logger.error(f"Round {round_num} failed")
                
                # Simulate time between rounds
                time.sleep(1)
                
        except KeyboardInterrupt:
            self.logger.info("Simulation interrupted by user")
        except Exception as e:
            self.logger.error(f"Simulation failed: {str(e)}")
        finally:
            self.is_running = False
            self._cleanup_simulation()
    
    def _evaluate_global_model(self) -> Dict:
        """Evaluate the global model on test data."""
        global_weights = self.federated_server.get_global_model_weights()
        if not global_weights:
            return {}
        
        # Create a temporary model for evaluation
        from sklearn.linear_model import LogisticRegression
        temp_model = LogisticRegression(**LIGHTWEIGHT_MODELS[DEFAULT_MODEL])
        
        try:
            # Set model parameters
            if 'coef_' in global_weights:
                temp_model.coef_ = global_weights['coef_']
            if 'intercept_' in global_weights:
                temp_model.intercept_ = global_weights['intercept_']
            if 'classes_' in global_weights:
                temp_model.classes_ = global_weights['classes_']
            
            # Evaluate on global test set
            X_test = self.global_test_data['X_test']
            y_test = self.global_test_data['y_test']
            
            start_time = time.time()
            y_pred = temp_model.predict(X_test)
            inference_time = time.time() - start_time
            
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            metrics = {
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y_test, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y_test, y_pred, average='weighted', zero_division=0),
                'inference_time': inference_time,
                'test_samples': len(y_test)
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Global model evaluation failed: {str(e)}")
            return {}
    
    def _collect_device_metrics(self) -> List[Dict]:
        """Collect metrics from all edge devices."""
        device_metrics = []
        
        for device in self.edge_devices:
            status = device.get_device_status()
            device_metrics.append(status)
        
        return device_metrics
    
    def _store_round_results(self, round_num: int, round_results: Dict, 
                           global_metrics: Dict, device_metrics: List[Dict]):
        """Store results from the current round."""
        self.simulation_results['rounds'].append({
            'round': round_num,
            'timestamp': datetime.now().isoformat(),
            'round_results': round_results,
            'global_metrics': global_metrics
        })
        
        self.simulation_results['device_metrics'].append({
            'round': round_num,
            'devices': device_metrics
        })
        
        self.simulation_results['global_metrics'].append(global_metrics)
        
        if 'communication_cost' in round_results:
            self.simulation_results['communication_costs'].append(round_results['communication_cost'])
    
    def _print_round_summary(self, round_num: int, round_results: Dict, global_metrics: Dict):
        """Print summary of the current round."""
        print(f"\n--- Round {round_num} Summary ---")
        print(f"Participating devices: {round_results.get('participating_devices', 0)}")
        print(f"Round time: {round_results.get('round_time', 0):.2f}s")
        print(f"Communication cost: {round_results.get('communication_cost', 0):.4f}s")
        
        if global_metrics:
            print(f"Global accuracy: {global_metrics.get('accuracy', 0):.4f}")
            print(f"Global F1-score: {global_metrics.get('f1_score', 0):.4f}")
            print(f"Inference time: {global_metrics.get('inference_time', 0):.4f}s")
        
        # Device statistics
        successful_devices = round_results.get('training_results', {}).get('successful_devices', 0)
        failed_devices = round_results.get('training_results', {}).get('failed_devices', 0)
        print(f"Training: {successful_devices} successful, {failed_devices} failed")
    
    def _start_real_time_monitoring(self):
        """Start real-time monitoring thread."""
        if ENABLE_REAL_TIME_PLOTS:
            self.monitoring_thread = threading.Thread(target=self._real_time_monitoring_worker)
            self.monitoring_thread.daemon = True
            self.monitoring_thread.start()
    
    def _real_time_monitoring_worker(self):
        """Worker thread for real-time monitoring."""
        plt.ion()
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        fig.suptitle('Real-time Edge IDS Federated Learning Monitoring')
        
        while self.is_running:
            try:
                # Update plots every few seconds
                time.sleep(PLOT_UPDATE_INTERVAL)
                self._update_monitoring_plots(axes)
                plt.pause(0.1)
            except Exception as e:
                self.logger.error(f"Real-time monitoring error: {str(e)}")
    
    def _update_monitoring_plots(self, axes):
        """Update real-time monitoring plots."""
        if not self.simulation_results['global_metrics']:
            return
        
        rounds = list(range(1, len(self.simulation_results['global_metrics']) + 1))
        accuracies = [m.get('accuracy', 0) for m in self.simulation_results['global_metrics']]
        f1_scores = [m.get('f1_score', 0) for m in self.simulation_results['global_metrics']]
        comm_costs = self.simulation_results['communication_costs']
        
        # Clear and update plots
        for ax in axes.flat:
            ax.clear()
        
        # Accuracy plot
        axes[0, 0].plot(rounds, accuracies, 'b-o')
        axes[0, 0].set_title('Global Model Accuracy')
        axes[0, 0].set_xlabel('Round')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].grid(True)
        
        # F1-score plot
        axes[0, 1].plot(rounds, f1_scores, 'g-o')
        axes[0, 1].set_title('Global Model F1-Score')
        axes[0, 1].set_xlabel('Round')
        axes[0, 1].set_ylabel('F1-Score')
        axes[0, 1].grid(True)
        
        # Communication cost plot
        if comm_costs:
            axes[1, 0].plot(rounds[:len(comm_costs)], comm_costs, 'r-o')
            axes[1, 0].set_title('Communication Cost per Round')
            axes[1, 0].set_xlabel('Round')
            axes[1, 0].set_ylabel('Cost (seconds)')
            axes[1, 0].grid(True)
        
        # Device participation
        if self.simulation_results['rounds']:
            participation = [r['round_results'].get('participating_devices', 0) 
                           for r in self.simulation_results['rounds']]
            axes[1, 1].plot(rounds[:len(participation)], participation, 'm-o')
            axes[1, 1].set_title('Device Participation')
            axes[1, 1].set_xlabel('Round')
            axes[1, 1].set_ylabel('Number of Devices')
            axes[1, 1].grid(True)
        
        plt.tight_layout()
    
    def _update_real_time_plots(self, round_num: int, global_metrics: Dict):
        """Update real-time plots with new data."""
        if ENABLE_REAL_TIME_PLOTS:
            self.real_time_queue.put({
                'round': round_num,
                'metrics': global_metrics
            })
    
    def _cleanup_simulation(self):
        """Cleanup simulation resources."""
        self.logger.info("Cleaning up simulation resources...")
        
        # Cleanup edge devices
        for device in self.edge_devices:
            device.cleanup()
        
        # Save final results
        self.save_simulation_results()
        
        self.logger.info("Simulation cleanup completed")
    
    def save_simulation_results(self, filename: str = None):
        """Save simulation results to file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{RESULTS_DIR}/edge_ids_simulation_{timestamp}.json"
        
        try:
            # Add summary statistics
            self.simulation_results['summary'] = self._generate_summary_statistics()
            
            with open(filename, 'w') as f:
                json.dump(self.simulation_results, f, indent=2, default=str)
            
            self.logger.info(f"Simulation results saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Failed to save results: {str(e)}")
    
    def _generate_summary_statistics(self) -> Dict:
        """Generate summary statistics for the simulation."""
        if not self.simulation_results['global_metrics']:
            return {}
        
        accuracies = [m.get('accuracy', 0) for m in self.simulation_results['global_metrics']]
        f1_scores = [m.get('f1_score', 0) for m in self.simulation_results['global_metrics']]
        
        return {
            'total_rounds': len(self.simulation_results['rounds']),
            'final_accuracy': accuracies[-1] if accuracies else 0,
            'max_accuracy': max(accuracies) if accuracies else 0,
            'avg_accuracy': np.mean(accuracies) if accuracies else 0,
            'final_f1_score': f1_scores[-1] if f1_scores else 0,
            'max_f1_score': max(f1_scores) if f1_scores else 0,
            'avg_f1_score': np.mean(f1_scores) if f1_scores else 0,
            'total_communication_cost': sum(self.simulation_results['communication_costs']),
            'avg_communication_cost': np.mean(self.simulation_results['communication_costs']) if self.simulation_results['communication_costs'] else 0,
            'num_devices': len(self.edge_devices),
            'simulation_completed': True
        }

def main():
    """Main function to run the simulation."""
    print("🚀 Starting Edge IDS Federated Learning Simulation")
    print("=" * 60)
    
    # Create and run simulation
    simulation = EdgeIDSSimulation()
    
    try:
        # Initialize simulation
        simulation.initialize_simulation()
        
        # Run federated learning
        simulation.run_federated_learning()
        
        print("\n✅ Simulation completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Simulation failed: {str(e)}")
        logging.error(f"Simulation failed: {str(e)}")

if __name__ == "__main__":
    main()
