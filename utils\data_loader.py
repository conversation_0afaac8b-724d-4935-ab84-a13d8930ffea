import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import SimpleImputer
import os

def load_and_preprocess_data(file_path):
    """
    Loads the raw dataset, preprocesses it (handles missing values,
    encodes labels, scales numerical features), and saves the processed data.

    Args:
        file_path (str): The path to the raw dataset CSV file.

    Returns:
        tuple: (processed_df, label_column, label_encoder) where:
            - processed_df: The preprocessed DataFrame
            - label_column: The name of the target label column
            - label_encoder: The fitted LabelEncoder used for encoding labels
    """
    print(f"Loading raw data from {file_path}...")
    try:
        # Use low_memory=False to handle mixed types better, though it uses more RAM
        df = pd.read_csv(file_path, low_memory=False)
    except FileNotFoundError:
        print(f"Error: Dataset not found at {file_path}. Please ensure 'raw_dataset.csv' is in the 'data/' folder.")
        return None

    print("Initial data shape:", df.shape)
    print("Columns:", df.columns.tolist())

    # Standardize column names: remove leading/trailing spaces, replace spaces with underscores, to lower case
    df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_').str.replace('/', '_')

    # Identify and drop duplicate rows
    initial_rows = df.shape[0]
    df.drop_duplicates(inplace=True)
    print(f"Dropped {initial_rows - df.shape[0]} duplicate rows.")

    # --- Identify Target Label Column ---
    label_col_candidates = ['label', 'attack', 'class']
    label_column = None
    for col in label_col_candidates:
        if col in df.columns:
            label_column = col
            break

    if label_column is None:
        print("Error: Could not find a suitable label column (e.g., 'label', 'attack', 'class').")
        print("Please check your dataset columns and rename the target column if necessary.")
        return None

    print(f"Using '{label_column}' as the target label column.")
    print("Unique labels before encoding:", df[label_column].unique())

    # --- Feature Engineering and Type Conversion ---
    # First, separate features and target temporarily
    X = df.drop(columns=[label_column])
    y = df[label_column]

    # Convert all feature columns to numeric, coercing errors to NaN
    # This is crucial for handling mixed types and ensuring numerical columns are treated as such
    for col in X.columns:
        # Attempt to convert to numeric. If it fails, leave as object/NaN.
        # This will also convert 'Infinity' to NaN if it appears as a string representation
        X[col] = pd.to_numeric(X[col], errors='coerce')

    print("Attempted to convert all feature columns to numeric.")

    # Replace infinite values with NaN before imputation
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    print("Replaced infinite values with NaN in features.")

    # Drop columns that are entirely NaN after coercion/infinity replacement or have too many missing values
    initial_feature_cols = X.shape[1]
    X.dropna(axis=1, thresh=len(X) * 0.3, inplace=True) # Keep columns with at least 30% non-NaN values
    print(f"Dropped {initial_feature_cols - X.shape[1]} feature columns due to too many missing values or all NaN after conversion.")

    # Impute missing values for numerical columns using the mean
    numerical_cols = X.select_dtypes(include=np.number).columns
    if not numerical_cols.empty:
        imputer = SimpleImputer(strategy='mean')
        # Use .copy() to avoid SettingWithCopyWarning and ensure operations are on the main DataFrame
        X_numerical_imputed = pd.DataFrame(imputer.fit_transform(X[numerical_cols]), columns=numerical_cols, index=X.index)
        X[numerical_cols] = X_numerical_imputed
        print("Imputed missing numerical values with the mean.")
    else:
        print("No numerical columns found after conversion and dropping, or all were imputed.")

    # Re-evaluate categorical columns (those still 'object' dtype after numeric conversion attempt)
    categorical_cols = X.select_dtypes(include='object').columns
    if not categorical_cols.empty:
        print("Performing One-Hot Encoding on remaining categorical features.")
        # Only encode if less than 50 unique values, otherwise drop
        cols_to_encode = [col for col in categorical_cols if X[col].nunique() < 50]
        cols_to_drop = [col for col in categorical_cols if X[col].nunique() >= 50]

        if cols_to_encode:
            X = pd.get_dummies(X, columns=cols_to_encode, prefix=cols_to_encode, drop_first=True)
            print(f"One-Hot Encoded {len(cols_to_encode)} categorical features.")
        if cols_to_drop:
            X = X.drop(columns=cols_to_drop)
            print(f"Dropped {len(cols_to_drop)} high cardinality categorical features: {', '.join(cols_to_drop)}.")
    else:
        print("No categorical features remaining for encoding.")

    # Scale numerical features (after imputation and one-hot encoding, as new numerical columns might appear)
    # Ensure numerical_cols is re-identified after get_dummies if any were created
    numerical_cols_final = X.select_dtypes(include=np.number).columns
    if not numerical_cols_final.empty:
        scaler = StandardScaler()
        # Ensure we're operating on a DataFrame/copy to avoid warnings
        X_scaled = pd.DataFrame(scaler.fit_transform(X[numerical_cols_final]), columns=numerical_cols_final, index=X.index)
        X[numerical_cols_final] = X_scaled
        print("Final numerical features scaled using StandardScaler.")
    else:
        print("No numerical features found for final scaling.")

    # Ensure all column names are strings for consistency
    X.columns = X.columns.astype(str)

    # --- Encode Target Label (Globally fitted) ---
    # Handle NaN values in the label column *before* encoding
    if y.isnull().any():
        print(f"Warning: NaN values found in '{label_column}' column. Filling with mode.")
        y = y.fillna(y.mode()[0])

    # Fit LabelEncoder on the full 'y' before splitting to ensure all classes are known
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)
    print("Labels encoded to numerical values (globally fitted).")
    print("Encoded labels map:", dict(zip(le.classes_, le.transform(le.classes_))))
    
    # Save the processed data
    processed_df = pd.concat([X, pd.Series(y_encoded, name=label_column, index=X.index)], axis=1)
    processed_file_path = os.path.join(os.path.dirname(file_path), 'processed_dataset.csv')
    processed_df.to_csv(processed_file_path, index=False)
    print(f"Processed data saved to {processed_file_path}")
    print("Final processed data shape:", processed_df.shape)

    return processed_df, label_column, le # Return the fitted LabelEncoder as well


def get_client_data(processed_df, num_clients, label_column):
    """
    Splits the processed dataset into subsets for each client.
    Ensures that each client gets a distinct part of the data.

    Args:
        processed_df (pd.DataFrame): The preprocessed dataset.
        num_clients (int): The number of simulated edge clients.
        label_column (str): The name of the target label column.

    Returns:
        dict: A dictionary where keys are client IDs (e.g., 'client_1')
              and values are tuples of (X_train, X_test, y_train, y_test) for that client.
    """
    print(f"\nSplitting data for {num_clients} clients...")
    client_data = {}
    
    X = processed_df.drop(columns=[label_column])
    y = processed_df[label_column]

    try:
        # Check if 'y' has at least 2 samples for each class for stratification.
        # Ensure all classes from the global LabelEncoder are considered, even if
        # a specific client split doesn't have them. This is handled by global encoding.
        if y.nunique() > 1 and all(y.value_counts() >= num_clients):
            X_train_global, X_test_global, y_train_global, y_test_global = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            print("Global train-test split created (20% for global evaluation) with stratification.")
        else:
            print("Warning: Not enough samples per class for full stratification across clients. Using non-stratified split for global split.")
            X_train_global, X_test_global, y_train_global, y_test_global = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
        
        # Now split the global training set among clients
        total_samples = X_train_global.shape[0]
        samples_per_client = total_samples // num_clients
        
        for i in range(num_clients):
            start_idx = i * samples_per_client
            end_idx = start_idx + samples_per_client
            
            if i == num_clients - 1: # Last client gets remaining samples
                X_client_data = X_train_global.iloc[start_idx:]
                y_client_data = y_train_global.iloc[start_idx:]
            else:
                X_client_data = X_train_global.iloc[start_idx:end_idx]
                y_client_data = y_train_global.iloc[start_idx:end_idx]
            
            # Further split client data into local train and test sets
            # Use stratification if possible for local client splits
            if X_client_data.shape[0] > 1 and y_client_data.nunique() > 1 and all(y_client_data.value_counts() >= 2):
                X_train_client, X_test_client, y_train_client, y_test_client = train_test_split(
                    X_client_data, y_client_data, test_size=0.2, random_state=42, stratify=y_client_data
                )
                print(f"Client {i+1} data shape: Train {X_train_client.shape}, Test {X_test_client.shape} (Stratified local split)")
            elif X_client_data.shape[0] > 1:
                 X_train_client, X_test_client, y_train_client, y_test_client = train_test_split(
                    X_client_data, y_client_data, test_size=0.2, random_state=42
                )
                 print(f"Client {i+1} data shape: Train {X_train_client.shape}, Test {X_test_client.shape} (Non-stratified local split)")
            else: # Handle case with very few samples for a client
                X_train_client, y_train_client = X_client_data, y_client_data
                X_test_client, y_test_client = pd.DataFrame(columns=X.columns), pd.Series(dtype=int) # Ensure empty test set has correct columns
                print(f"Client {i+1} data shape: Train {X_train_client.shape}, Test {X_test_client.shape} (Insufficient data for local split)")


            client_data[f'client_{i+1}'] = (X_train_client, X_test_client, y_train_client, y_test_client)
        
        # Add the global test set for final evaluation
        client_data['global_test_set'] = (X_test_global, y_test_global)
        print(f"Global test set shape: X {X_test_global.shape}, y {y_test_global.shape}")

    except ValueError as e:
        print(f"Error during data splitting: {e}. Falling back to a non-stratified global split.")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42 # Global test set
        )
        client_data['global_test_set'] = (X_test, y_test)
        
        total_samples = X_train.shape[0]
        samples_per_client = total_samples // num_clients
        
        for i in range(num_clients):
            start_idx = i * samples_per_client
            end_idx = start_idx + samples_per_client
            
            if i == num_clients - 1:
                X_client_data = X_train.iloc[start_idx:]
                y_client_data = y_train.iloc[start_idx:]
            else:
                X_client_data = X_train.iloc[start_idx:end_idx]
                y_client_data = y_train.iloc[start_idx:end_idx]
            
            if X_client_data.shape[0] > 1:
                X_train_client, X_test_client, y_train_client, y_test_client = train_test_split(
                    X_client_data, y_client_data, test_size=0.2, random_state=42
                )
            else:
                X_train_client, y_train_client = X_client_data, y_client_data
                X_test_client, y_test_client = pd.DataFrame(columns=X.columns), pd.Series(dtype=int)

            client_data[f'client_{i+1}'] = (X_train_client, X_test_client, y_train_client, y_test_client)
            print(f"Client {i+1} data shape: Train {X_train_client.shape}, Test {X_test_client.shape} (Fallback non-stratified local split)")

    return client_data

if __name__ == '__main__':
    # This block runs only when data_loader.py is executed directly
    script_dir = os.path.dirname(__file__)
    project_root = os.path.abspath(os.path.join(script_dir, '..')) # Go up one level from utils to project root
    data_path = os.path.join(project_root, 'data', 'raw_dataset.csv')
    
    # 1. Load and preprocess data
    processed_df, label_col, label_encoder = load_and_preprocess_data(data_path)

    if processed_df is not None:
        # 2. Get data for 3 clients
        client_data_sets = get_client_data(processed_df, num_clients=3, label_column=label_col)

        print("\n--- Sample Client Data Shapes ---")
        for client_id, data_tuple in client_data_sets.items():
            if client_id != 'global_test_set':
                X_train, X_test, y_train, y_test = data_tuple
                print(f"{client_id}: Train X {X_train.shape}, Train y {y_train.shape}, Test X {X_test.shape}, Test y {y_test.shape}")
        
        X_test_global, y_test_global = client_data_sets['global_test_set']
        print(f"Global Test Set: X {X_test_global.shape}, y {y_test_global.shape}")